import aiohttp
import logging
import os
from fastapi import FastAPI, Request, HTTPException, Depends, APIRouter
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from contextlib import asynccontextmanager
from pathlib import Path
import aiofiles
from pywatcher.api.dependencies import get_steam_py_client
import asyncio
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
import json
from pywatcher.services.email_service import send_email
# from pywatcher.config import settings  # 注释掉这行，使用其他方式获取配置

# 配置日志 - 改为更详细的调试级别
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 导入模块
from pywatcher.database.session import db_init
from pywatcher.api import dependencies
from pywatcher.api.endpoints import auth, accounts, games, chests, orders, web_accounts, price_comparison, steam
from pywatcher.crud.config_manager import initialize_config
from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.crud.cookie_pool_manager import <PERSON><PERSON>PoolManager
from pywatcher.crud.order_manager import get_order_manager
from pywatcher.models.order import OrderRecord
from datetime import datetime, timedelta
import uuid

async def load_env_async(path: str = ".env"):
    """Asynchronously reads a .env file and loads variables into os.environ."""
    try:
        async with aiofiles.open(path, "r", encoding="utf-8") as f:
            async for line in f:
                line = line.strip()
                if not line or line.startswith("#") or "=" not in line:
                    continue
                key, value = line.split("=", 1)
                key = key.strip()
                # 移除可能存在的引号
                value = value.strip().strip("\"'")
                if key not in os.environ:
                    os.environ[key] = value
        logger.info(".env file loaded asynchronously.")
    except FileNotFoundError:
        logger.warning(f".env file not found at '{path}', skipping.")
    except Exception as e:
        logger.error(f"Error loading .env file: {e}")

# --- 应用生命周期管理 ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时
    # 将所有可能涉及IO的初始化操作都放入lifespan
    
    # 1. 挂载静态文件和模板
    # 这会执行同步的 os.path.isdir 检查, 放在lifespan中更安全
    base_dir = Path(__file__).resolve().parent.parent
    app.state.templates = Jinja2Templates(directory=base_dir / "templates")
    app.mount("/static", StaticFiles(directory=base_dir / "static"), name="static")
    
    # 2. 异步加载环境变量
    # 直接从pywatcher目录加载.env文件，而不是从父目录
    await load_env_async(base_dir / ".env")
    
    logger.info("应用启动中...")
    # 3. 初始化数据库
    await db_init()
    logger.info("数据库初始化完成。")
    
    # 4. 初始化价格比较数据库
    from pywatcher.crud.price_comparison_manager import get_price_manager
    await get_price_manager()
    logger.info("价格比较数据库初始化完成。")
    
    # 5. 异步加载应用配置
    await initialize_config()
    logger.info("应用配置加载完成。")
    # 6. 创建全局 aiohttp.ClientSession
    dependencies.client_session = aiohttp.ClientSession()
    logger.info("全局 ClientSession 已创建。")
    
    yield
    
    # 应用关闭时
    logger.info("应用关闭中...")
    # 1. 关闭全局 aiohttp.ClientSession
    if dependencies.client_session and not dependencies.client_session.closed:
        await dependencies.client_session.close()
        logger.info("全局 ClientSession 已关闭。")

# --- FastAPI 应用实例 ---
app = FastAPI(
    title="PyWatcher Web App",
    description="一个用于监控和自动购买 Steam 市场物品的应用。",
    lifespan=lifespan,
    debug=True  # 启用调试模式
)

# --- API 路由器 ---
app.include_router(auth.router, prefix="/api")
app.include_router(accounts.router, prefix="/api")
# app.include_router(web_accounts.router, prefix="/api/web-accounts")  # Web账户功能已禁用
app.include_router(games.router, prefix="/api")
app.include_router(chests.router, prefix="/api")
app.include_router(orders.router, prefix="/api")
app.include_router(price_comparison.router, prefix="/api")
app.include_router(steam.router)

# --- 直接路由处理器，用于特定API路径 ---
@app.get("/api/xboot/chest/showOne", include_in_schema=True)
async def direct_chest_detail(
    chestId: str,
    request: Request,
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """直接处理肥皂盒详情请求"""
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        response = await client.get_chest_details(chest_id=chestId)
        
        if response.get("code") == 200:
            return response
        else:
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒详情失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取肥皂盒详情失败: {str(e)}")

@app.get("/api/xboot/chest/showGame", include_in_schema=True)
async def direct_chest_games(
    chestId: str,
    pageNumber: int = 1,
    pageSize: int = 100,
    sort: str = "lv",
    order: str = "asc",
    request: Request = None,
    client: SteamPyClient = Depends(get_steam_py_client)
):
    """直接处理肥皂盒游戏列表请求"""
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        response = await client.get_chest_games(
            chest_id=chestId,
            page_number=pageNumber,
            page_size=pageSize,
            sort_order=sort,
            order_direction=order
        )
        
        if response.get("code") == 200:
            return response
        else:
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "获取肥皂盒游戏列表失败")
            )
    except Exception as e:
        logger.error(f"获取肥皂盒游戏列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取肥皂盒游戏列表失败: {str(e)}")

# --- 页面路由 ---
@app.get("/", include_in_schema=False)
async def page_dashboard(request: Request):
    logger.debug("访问仪表盘页面")
    return request.app.state.templates.TemplateResponse("pages/dashboard.html", {"request": request})


@app.get("/accounts", include_in_schema=False)
async def page_accounts(request: Request):
    logger.debug("访问账户管理页面")
    return request.app.state.templates.TemplateResponse("pages/accounts.html", {"request": request})

@app.get("/games", include_in_schema=False)
async def page_games(request: Request):
    logger.debug("访问游戏列表页面")
    return request.app.state.templates.TemplateResponse("pages/games.html", {"request": request})

@app.get("/favorites", include_in_schema=False)
async def page_favorites(request: Request):
    logger.debug("访问游戏收藏页面")
    return request.app.state.templates.TemplateResponse("pages/favorites.html", {"request": request})

@app.get("/chests", include_in_schema=False)
async def page_chests(request: Request):
    logger.debug("访问肥皂盒页面")
    return request.app.state.templates.TemplateResponse("pages/chests.html", {"request": request})

@app.get("/chest-detail", include_in_schema=False)
async def page_chest_detail(request: Request):
    logger.debug("访问肥皂盒详情页面")
    return request.app.state.templates.TemplateResponse("pages/chest_detail.html", {"request": request})

@app.get("/monitor", include_in_schema=False)
async def page_monitor(request: Request):
    logger.debug("访问监控中心页面")
    return request.app.state.templates.TemplateResponse("pages/monitor.html", {"request": request})

@app.get("/price-comparison", include_in_schema=False)
async def page_price_comparison(request: Request):
    logger.debug("访问比价页面")
    return request.app.state.templates.TemplateResponse("pages/price_comparison.html", {"request": request})

@app.get("/redeem", include_in_schema=False)
async def page_redeem(request: Request):
    logger.debug("访问Steam Key激活页面")
    return request.app.state.templates.TemplateResponse("pages/redeem.html", {"request": request})

@app.post("/api/send-email", include_in_schema=True)
async def send_notification_email(
    request: Request,
    email_data: dict,
):
    """
    发送通知邮件的API端点
    接收主题和内容，使用系统配置的邮件发送
    """
    try:
        if not email_data or "subject" not in email_data or "body" not in email_data:
            return {"success": False, "message": "邮件数据不完整，需要subject和body字段"}
        
        subject = email_data["subject"]
        body = email_data["body"]
        
        # 使用异步任务发送邮件，不阻塞响应
        async def send_email_task():
            try:
                result = await send_email(subject, body)
                logger.info(f"通知邮件 '{subject}' 发送结果: {result}")
            except Exception as e:
                logger.error(f"发送邮件通知失败: {e}")
        
        # 创建异步任务，不等待完成
        asyncio.create_task(send_email_task())
        
        return {"success": True, "message": "邮件发送请求已提交"}
    except Exception as e:
        logger.error(f"处理邮件发送请求时出错: {e}")
        return {"success": False, "message": f"邮件发送失败: {str(e)}"}

@app.post("/api/xboot/steamKeyOrder/payOrder", include_in_schema=True)
async def direct_place_order(
    request: Request,
    saleId: str,
    payType: str,
    promoCodeId: str = "",
    walletFlag: str = "",
    version: str = "v1",
    is_main_account: bool = True,
    sellerSold: int = 0,
    sellerName: str = "",
    session: aiohttp.ClientSession = Depends(dependencies.get_client_session),
    cookie_manager: CookiePoolManager = Depends(dependencies.get_cookie_pool_manager)
):
    """
    直接处理下单购买请求
    使用对应的下单账号(主账号或副账号)发送请求
    
    参数:
    - saleId: 卖家ID
    - payType: 支付方式(AA=支付宝, WX=微信)
    - promoCodeId: 优惠码ID(可选)
    - walletFlag: 是否使用余额(useBalance=使用)
    - version: API版本
    - is_main_account: 是否使用主账号下单(true=主账号，false=副账号)
    """
    
    try:
        # 获取下单账号类型
        target_type = "order_primary" if is_main_account else "order_secondary"
        account_type_name = "下单主账号" if is_main_account else "下单副账号"
        
        # 获取所有账号
        all_accounts = cookie_manager.get_all_mobile_accounts()
        
        # 查找指定类型的账号
        target_account = None
        for account in all_accounts:
            if account.account_type == target_type:
                target_account = account
                break
        
        if not target_account:
            # 没找到对应类型的账号，给出明确的错误信息
            logger.error(f"No {target_type} account found.")
            raise HTTPException(
                status_code=404,
                detail=f"未配置{account_type_name}，请在账号管理中设置。"
            )
        
        if not target_account.access_token:
            logger.error(f"Account '{target_account.name}' (type: {target_account.account_type}) does not have an access token.")
            raise HTTPException(
                status_code=401,
                detail=f"账号 '{target_account.name}' 未登录或token失效，请重新登录。"
            )
        
        logger.info(f"Found order account: {target_account.name} (type: {target_account.account_type})")
        order_account_token = target_account.access_token
        
        # 使用该token创建临时的SteamPyClient实例
        temp_client = SteamPyClient(session=session, access_token=order_account_token)
        
        # 使用该实例发送下单请求
        response = await temp_client.place_order(
            sale_id=saleId,
            pay_type=payType,
            wallet_flag=walletFlag
        )
        
        logger.info(f"使用{account_type_name}下单，订单ID: {saleId}, 支付方式: {payType}, 使用余额: {'是' if walletFlag else '否'}")
        
        if response.get("success"):
            try:
                # 提取订单信息
                order_id = response.get("result", {}).get("orderId", str(uuid.uuid4()))
                pay_url = response.get("result", {}).get("form")
                pay_price = float(response.get("result", {}).get("payPrice", 0))
                
                # 不再尝试通过API获取游戏信息，直接使用saleId作为游戏ID
                game_id = saleId
                # 这里需要从response中提取gameName，如果不存在则使用默认值
                game_name = response.get("result", {}).get("gameName", "未知游戏")
                
                # 如果游戏名为空，再尝试从卖家界面已知的游戏名称获取(前端可能已经传递)
                if game_name == "未知游戏" and "gameName" in request.query_params:
                    game_name = request.query_params.get("gameName", "未知游戏")
                
                # 创建订单记录
                order_manager = await get_order_manager()
                current_time = datetime.now()
                # 设置过期时间：未支付5分钟，已支付15分钟
                is_paid = pay_price == 0
                expire_minutes = 15 if is_paid else 5
                expire_time = current_time.replace(microsecond=0) + timedelta(minutes=expire_minutes)
                
                order_record = OrderRecord(
                    id=order_id,
                    game_id=game_id,
                    game_name=game_name,
                    price=pay_price,
                    account_id=target_account.account,
                    account_name=target_account.name or target_account.account,
                    status="已支付" if is_paid else "待支付",
                    order_time=current_time,
                    access_token=order_account_token,
                    sale_id=saleId,
                    pay_url=pay_url,
                    expire_time=expire_time
                )
                await order_manager.add_order(order_record)
                logger.info(f"成功保存订单记录: {order_id}, 游戏: {game_name}")
                
                # 发送邮件通知
                alipay_url = response.get("result", {}).get("form", "N/A")
                pay_price = response.get("result", {}).get("payPrice", "N/A")
                account_type_name = "主账号" if is_main_account else "副账号"
                
                # 获取游戏信息
                # 如果游戏名在请求中有提供，则使用请求中的游戏名
                if "gameName" in request.query_params:
                    game_name = request.query_params.get("gameName")
                    logger.info(f"使用请求参数中的游戏名称: {game_name}")
                # 否则从响应的gameInfo尝试获取
                else:
                    game_info = response.get("result", {}).get("gameInfo", {})
                    if game_info and "gameName" in game_info:
                        game_name = game_info.get("gameName")
                        logger.info(f"从响应中获取到游戏名称: {game_name}")
                    # 使用订单记录中已保存的游戏名（之前已经尝试获取过）
                    elif game_name == "未知游戏":
                        logger.warning("未能从API响应获取游戏名称，使用默认值")
                
                # 判断是CDK下单还是肥皂盒下单
                # 通过请求路径或参数判断
                is_chest_order = False
                referer = request.headers.get("referer", "")
                if "chest" in referer.lower() or request.query_params.get("orderType") == "chest":
                    is_chest_order = True
                
                if is_chest_order:
                    # 肥皂盒下单邮件模板
                    email_subject = f"【肥皂盒下单成功】肥皂盒ID: {saleId}"
                    email_body = (
                        f"使用{account_type_name}对肥皂盒下单成功。\n"
                        f"肥皂盒ID: {saleId}\n"
                        f"支付方式: {payType}, 余额使用: {'是' if walletFlag else '否'}\n"
                        f"支付金额: ¥{pay_price}\n\n"
                    )
                else:
                    # 识别saleId类型
                    sale_type = "个人上架" if saleId.startswith('K') else "肥皂盒转挂单"
                    
                    # CDK下单邮件模板
                    email_subject = f"【CDK下单成功】{game_name}"
                    email_body = (
                        f"使用{account_type_name}下单CDK成功。\n"
                        f"游戏名称: {game_name}\n"
                        f"订单号: {saleId}\n"
                        f"订单类型: {sale_type}\n"
                        f"支付方式: {payType}, 余额使用: {'是' if walletFlag else '否'}\n"
                        f"支付金额: ¥{pay_price}\n"
                    )
                    
                    # 如果有卖家信息，添加到邮件中
                    if sellerName:
                        email_body += f"卖家名称: {sellerName}\n"
                    if sellerSold >= 0:  # 修复：即使交易数为0也显示
                        email_body += f"卖家交易数: {sellerSold}\n"
                    
                    email_body += "\n"
                
                # 使用邮件服务发送通知 - 改为异步任务，不阻塞响应
                async def send_email_task():
                    try:
                        # 直接传递form字段的URL给邮件服务
                        await send_email(email_subject, email_body, payment_url=alipay_url)
                        logger.info(f"下单成功，邮件通知已发送: {email_subject}")
                    except Exception as e:
                        logger.error(f"发送邮件通知失败: {e}")
                
                # 创建异步任务，不等待完成
                asyncio.create_task(send_email_task())

                return response
            except Exception as e:
                logger.error(f"保存订单记录时出错: {e}", exc_info=True)
            
            return response
        else:
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "下单失败")
            )
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"下单过程中发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"下单失败: {str(e)}")

@app.post("/api/xboot/steamKeyOrder/checkPay", include_in_schema=True)
async def check_payment_status(
    request: Request,
    orderId: str,
    session: aiohttp.ClientSession = Depends(dependencies.get_client_session),
    cookie_manager: CookiePoolManager = Depends(dependencies.get_cookie_pool_manager)
):
    """
    检查订单支付状态
    
    参数:
    - orderId: 订单ID
    """
    
    try:
        # 从订单记录中获取下单使用的access_token
        order_manager = await get_order_manager()
        order_record = order_manager.get_order_by_id(orderId)
        
        if not order_record:
            raise HTTPException(
                status_code=404,
                detail=f"订单 {orderId} 不存在"
            )
        
        # 使用订单记录中的access_token创建SteamPyClient实例
        temp_client = SteamPyClient(session=session, access_token=order_record.access_token)
        
        # 调用支付状态检查API
        response = await temp_client.check_payment_status(orderId)
        
        if response.get("success"):
            return response
        else:
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "支付状态检查失败")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查支付状态时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"支付状态检查失败: {str(e)}")

@app.post("/api/xboot/steamKeyOrder/keyFirstV2", include_in_schema=True)
async def redeem_order_key(
    request: Request,
    session: aiohttp.ClientSession = Depends(dependencies.get_client_session),
    cookie_manager: CookiePoolManager = Depends(dependencies.get_cookie_pool_manager)
):
    """
    兑换订单激活码
    
    参数通过表单数据传递:
    - orderId: 订单ID
    - player: 玩家名称
    - password: 密码(可选)
    - saveLog: 是否保存日志
    """
    
    try:
        # 从表单数据中解析参数
        form_data = await request.form()
        orderId = form_data.get('orderId')
        player = form_data.get('player', 'HEIXS21')
        password = form_data.get('password', '')
        saveLog = form_data.get('saveLog', 'false').lower() == 'true'
        
        if not orderId:
            raise HTTPException(
                status_code=400,
                detail="缺少必需参数 orderId"
            )
        
        # 从订单记录中获取下单使用的access_token
        order_manager = await get_order_manager()
        order_record = order_manager.get_order_by_id(orderId)
        
        if not order_record:
            raise HTTPException(
                status_code=404,
                detail=f"订单 {orderId} 不存在"
            )
        
        # 使用订单记录中的access_token创建SteamPyClient实例
        temp_client = SteamPyClient(session=session, access_token=order_record.access_token)
        
        # 调用兑换激活码API
        response = await temp_client.redeem_key(
            order_id=orderId,
            player=player,
            password=password,
            save_log=saveLog
        )
        
        if response.get("success"):
            return response
        else:
            raise HTTPException(
                status_code=response.get("code", 500),
                detail=response.get("message", "激活码兑换失败")
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"兑换激活码时发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"激活码兑换失败: {str(e)}")

# 添加肥皂盒收藏页面路由
@app.get("/favorite-chests", response_class=HTMLResponse)
async def favorite_chests_page(request: Request):
    """肥皂盒收藏页面"""
    logger.debug("访问肥皂盒收藏页面")
    return request.app.state.templates.TemplateResponse("pages/favorite-chests.html", {"request": request})

# 定义静态路径
STATIC_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")

# 设置静态文件目录
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# 定义API路由
router = APIRouter()

# 用于肥皂盒下单的模型
class ChestOrderRequest(BaseModel):
    chestId: str
    draws: int = 1
    payType: str = "py"  # 支付方式: AA 或 py
    walletFlag: Optional[str] = None  # 使用余额: useBalance 或空字符串
    useMainAccount: bool = True  # 是否使用主账号
    orderType: Optional[str] = "chest"  # 订单类型: chest 或 cdk

@router.post("/chest/place-order")
async def place_chest_order(order_data: ChestOrderRequest, request: Request):
    """
    为肥皂盒下单的API端点。
    根据传入的参数和配置选择合适的账号进行下单。
    下单成功后会发送邮件通知结果。
    """
    try:
        # 获取下单账号类型
        target_type = "order_primary" if order_data.useMainAccount else "order_secondary"
        account_type_name = "下单主账号" if order_data.useMainAccount else "下单副账号"
        
        # 获取所有账号
        cookie_manager = dependencies.get_cookie_pool_manager()
        all_accounts = cookie_manager.get_all_mobile_accounts()
        
        # 查找指定类型的账号
        target_account = None
        for account in all_accounts:
            if account.account_type == target_type:
                target_account = account
                break
        
        if not target_account:
            # 没找到对应类型的账号，给出明确的错误信息
            logger.error(f"No {target_type} account found.")
            return {"success": False, "message": f"未配置{account_type_name}，请在账号管理中设置。"}
        
        if not target_account.access_token:
            logger.error(f"Account '{target_account.name}' (type: {target_account.account_type}) does not have an access token.")
            return {"success": False, "message": f"账号 '{target_account.name}' 未登录或token失效，请重新登录。"}
        
        logger.info(f"Found order account: {target_account.name} (type: {target_account.account_type})")
        account_token = target_account.access_token
        
        # 构建请求参数
        payload = {
            "chestId": order_data.chestId,
            "draws": order_data.draws,
            "payType": order_data.payType,
        }
        
        if order_data.walletFlag:
            payload["walletFlag"] = order_data.walletFlag
        
        # 发送下单请求
        logger.info(f"准备使用{account_type_name}下单: {payload}")
        headers = {"accesstoken": account_token, "Content-Type": "application/x-www-form-urlencoded"}
        
        async with aiohttp.ClientSession() as session:
            async with session.post("https://steampy.com/xboot/blOrder/payOrder", data=payload, headers=headers) as resp:
                order_result = await resp.json()
        
        # 处理下单结果
        if order_result.get("success") and order_result.get("code") == 200:
            # 发送邮件通知
            alipay_url = order_result.get("result", {}).get("form", "N/A")
            pay_price = order_result.get("result", {}).get("payPrice", "N/A")
            account_type_name = "主账号" if order_data.useMainAccount else "副账号"
            
            # 获取游戏信息
            game_info = order_result.get("result", {}).get("gameInfo", {})
            game_name = "未知游戏"
            
            if game_info and "gameName" in game_info:
                game_name = game_info.get("gameName")
                logger.info(f"从响应中获取到游戏名称: {game_name}")
            # 如果响应中没有游戏名，检查请求参数
            elif request.query_params.get("gameName"):
                game_name = request.query_params.get("gameName")
                logger.info(f"使用请求参数中的游戏名称: {game_name}")
            
            # 判断是CDK下单还是肥皂盒下单
            # 通过请求路径或参数判断
            is_chest_order = False
            referer = request.headers.get("referer", "")
            if "chest" in referer.lower() or order_data.orderType == "chest":
                is_chest_order = True
            
            if is_chest_order:
                # 肥皂盒下单邮件模板
                email_subject = f"【肥皂盒下单成功】肥皂盒ID: {order_data.chestId}"
                email_body = (
                    f"使用{account_type_name}对肥皂盒下单成功。\n"
                    f"肥皂盒ID: {order_data.chestId}\n"
                    f"抽奖次数: {order_data.draws}\n"
                    f"支付方式: {order_data.payType}, 余额使用: {order_data.walletFlag if order_data.walletFlag else '不使用'}\n"
                    f"支付金额: {pay_price}\n"
                    f"支付链接: {alipay_url}\n"
                )
            else:
                # 识别saleId类型
                sale_type = "个人上架" if order_data.chestId.startswith('K') else "肥皂盒转挂单"
                
                # CDK下单邮件模板
                email_subject = f"【CDK下单成功】{game_name}"
                email_body = (
                    f"使用{account_type_name}下单CDK成功。\n"
                    f"游戏名称: {game_name}\n"
                    f"订单号: {order_data.chestId}\n"
                    f"订单类型: {sale_type}\n"
                    f"支付方式: {order_data.payType}, 余额使用: {order_data.walletFlag if order_data.walletFlag else '不使用'}\n"
                    f"支付金额: {pay_price}\n\n"
                    f"支付链接: {alipay_url}\n"
                )
            
            # 使用邮件服务发送通知 - 改为异步任务，不阻塞响应
            async def send_email_task():
                try:
                    # 直接传递form字段的URL给邮件服务
                    await send_email(email_subject, email_body, payment_url=alipay_url)
                    logger.info(f"下单成功，邮件通知已发送: {email_subject}")
                except Exception as e:
                    logger.error(f"发送邮件通知失败: {e}")
            
            # 创建异步任务，不等待完成
            asyncio.create_task(send_email_task())
            
            # 返回下单成功响应
            return {
                "success": True, 
                "message": f"使用{account_type_name}下单成功，支付链接已发送到邮箱！", 
                "result": {
                    "order_id": order_data.chestId,
                    "alipay_url": alipay_url
                }
            }
        else:
            error_message = order_result.get("message", "下单失败，未知错误。")
            logger.error(f"下单失败: {error_message}")
            return {"success": False, "message": error_message, "details": order_result}
            
    except aiohttp.ClientError as e:
        logger.error(f"网络请求错误: {e}")
        return {"success": False, "message": f"网络请求错误: {str(e)}"}
    except Exception as e:
        logger.error(f"处理请求时出错: {e}")
        return {"success": False, "message": f"处理请求时出错: {str(e)}"}

# 添加路由到应用
app.include_router(router, prefix="/api")

# 注册异步启动事件处理器
@app.on_event("startup")
async def startup_event():
    # 设置Jinja2模板
    templates_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates")
    app.state.templates = Jinja2Templates(directory=templates_dir)
# app = FastAPI()

# 挂载路由
# app.include_router(router, prefix="/api")

@app.get("/api/games/hot", include_in_schema=True)
async def get_hot_games(
    request: Request,
    client: SteamPyClient = Depends(get_steam_py_client),
    pageNumber: int = 1,
    pageSize: int = 14,
    sort: str = "cdkCount",
    order: str = "asc"
):
    """获取热门游戏列表"""
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据，请先登录。")
    
    try:
        # 构建请求参数
        params = {
            "pageNumber": pageNumber,
            "pageSize": pageSize,
            "sort": sort,
            "order": order
        }
        
        # 调用 SteamPyClient 的方法发送请求
        url = f"/steamGame/keyHot"
        response = await client._send_request("GET", url, params=params)
        
        # 直接返回原始响应，不做额外处理
        return response
    except Exception as e:
        logger.error(f"获取热门游戏列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取热门游戏列表失败: {str(e)}")




