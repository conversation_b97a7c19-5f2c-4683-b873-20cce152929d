// 全局变量
let monitoredGames = [];
let cdkMonitorTimers = {};
let isCdkMonitoring = false;
let cdkBatchMonitorTimer = null; // 统一批量监控定时器
let currentGameId = null;
let hotGamesCurrentPage = 1;
let hotGamesTotalPages = 1;
const hotGamesPageSize = 50;

// 添加DOM元素全局变量
let cdkMonitoredGamesContainer = null;
let cdkMonitorLogsContainer = null;
let cdkPriceChangesContainer = null;
let cdkSearchResults = null;

// 日志相关变量
const logBuffer = [];
const LOG_BUFFER_SIZE = 10;
let logUpdateScheduled = false;

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', initCdkMonitor);

// 初始化函数
function initCdkMonitor() {
    // 控制元素
    const toggleCdkMonitorBtn = document.getElementById('toggle-cdk-monitor');
    const addMonitorGameBtn = document.getElementById('add-monitor-game');
    const exportCdkGamesBtn = document.getElementById('export-cdk-games');
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const cdkNotifyChangesCheckbox = document.getElementById('cdk-notify-changes');
    const cdkAutoOrderCheckbox = document.getElementById('cdk-auto-order');
    cdkMonitoredGamesContainer = document.getElementById('cdk-monitored-games-container');
    cdkMonitorLogsContainer = document.getElementById('cdk-monitor-logs');
    const clearCdkLogsBtn = document.getElementById('clear-cdk-logs');
    cdkPriceChangesContainer = document.getElementById('cdk-price-changes');
    const clearCdkChangesBtn = document.getElementById('clear-cdk-changes');
    const saveCdkSettingsBtn = document.getElementById('save-cdk-settings');
    const viewHotGamesBtn = document.getElementById('view-hot-games');
    cdkSearchResults = document.getElementById('game-search-results');
    
    // 检查必要元素是否存在
    if (!cdkMonitoredGamesContainer) {
        console.error('CDK Monitor: 找不到监控游戏容器元素，ID: cdk-monitored-games-container');
        return;
    }
    
    if (!cdkMonitorLogsContainer) {
        console.error('CDK Monitor: 找不到日志容器元素，ID: cdk-monitor-logs');
        return;
    }
    
    // 模态框元素
    const exportModal = document.getElementById('export-modal');
    const closeExportModalXBtn = document.getElementById('close-export-modal-x');
    const closeExportModalBtn = document.getElementById('close-export-modal');
    const exportContentTextarea = document.getElementById('export-content');
    const copyExportBtn = document.getElementById('copy-export-btn');
    
    const addGameModal = document.getElementById('add-game-modal');
    const closeAddGameModalXBtn = document.getElementById('close-add-game-modal-x');
    const closeAddGameModalBtn = document.getElementById('close-add-game-modal');
    const gameSearchInput = document.getElementById('game-search-input');
    const searchGameBtn = document.getElementById('search-game-btn');
    
    const targetPriceModal = document.getElementById('set-target-price-modal');
    const closeTargetPriceModalXBtn = document.getElementById('close-target-price-modal-x');
    const closeTargetPriceModalBtn = document.getElementById('close-target-price-modal');
    const saveTargetPriceBtn = document.getElementById('save-target-price-btn');
    const clearTargetPriceBtn = document.getElementById('clear-target-price-btn');
    
    const hotGamesModal = document.getElementById('hot-games-modal');
    const closeHotGamesModalBtn = document.getElementById('close-hot-games-modal');
    const closeHotGamesModalXBtn = document.getElementById('close-hot-games-modal-x');

    // 初始化时，如果localStorage没有cdkAutoOrder设置，则默认设为true
    if (localStorage.getItem('cdkAutoOrder') === null) {
        localStorage.setItem('cdkAutoOrder', 'true');
    }
    
    // 确保全局自动下单复选框状态与localStorage同步
    if (cdkAutoOrderCheckbox) {
        cdkAutoOrderCheckbox.checked = localStorage.getItem('cdkAutoOrder') !== 'false';
        
        // 为自动下单复选框添加change事件处理器，变化时立即更新localStorage
        cdkAutoOrderCheckbox.addEventListener('change', function() {
            localStorage.setItem('cdkAutoOrder', this.checked);
            if (this.checked) {
                addCdkLog('自动下单已启用，当游戏价格达到目标时将自动下单', false, true);
            } else {
                addCdkLog('自动下单已禁用', false);
            }
        });
    }
    
    // 初始化
    try {
        loadMonitoredGames();
        loadCdkPriceChanges();
        loadCdkSettings();
    } catch (error) {
        console.error('CDK Monitor: 初始化过程出现错误', error);
        addCdkLog('初始化过程出现错误: ' + error.message, true);
    }
    
    // 绑定事件监听
    if (toggleCdkMonitorBtn) {
        toggleCdkMonitorBtn.addEventListener('click', function() {
            if (isCdkMonitoring) {
                stopCdkMonitoring();
            } else {
                startCdkMonitoring();
            }
        });
    }
    
    if (addMonitorGameBtn) {
        addMonitorGameBtn.addEventListener('click', function() {
            openAddGameModal();
        });
    }
    
    if (closeAddGameModalXBtn) {
        closeAddGameModalXBtn.addEventListener('click', function() {
            closeAddGameModal();
        });
    }
    
    if (closeAddGameModalBtn) {
        closeAddGameModalBtn.addEventListener('click', function() {
            closeAddGameModal();
        });
    }
    
    if (searchGameBtn) {
        searchGameBtn.addEventListener('click', function() {
            searchGames();
        });
    }
    
    if (gameSearchInput) {
        gameSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchGames();
            }
        });
    }
    
    if (clearCdkLogsBtn) {
        clearCdkLogsBtn.addEventListener('click', function() {
            if (cdkMonitorLogsContainer) {
                cdkMonitorLogsContainer.innerHTML = '<div class="text-muted text-center py-4">监控记录已清空...</div>';
            }
        });
    }
    
    if (clearCdkChangesBtn) {
        clearCdkChangesBtn.addEventListener('click', function() {
            if (cdkPriceChangesContainer) {
                cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无价格变动记录...</td></tr>';
                localStorage.removeItem('cdkPriceChanges');
            }
        });
    }
    
    if (saveCdkSettingsBtn) {
        saveCdkSettingsBtn.addEventListener('click', function() {
            saveCdkSettings();
        });
    }
    
    // 导出相关事件绑定
    if (exportCdkGamesBtn) {
        exportCdkGamesBtn.addEventListener('click', function() {
            openExportModal();
        });
    }
    
    if (closeExportModalXBtn) {
        closeExportModalXBtn.addEventListener('click', function() {
            closeExportModal();
        });
    }
    
    if (closeExportModalBtn) {
        closeExportModalBtn.addEventListener('click', function() {
            closeExportModal();
        });
    }
    
    if (copyExportBtn) {
        copyExportBtn.addEventListener('click', function() {
            copyExportContent();
        });
    }
    
    // 目标价格相关事件绑定
    if (closeTargetPriceModalXBtn) {
        closeTargetPriceModalXBtn.addEventListener('click', function() {
            closeTargetPriceModal();
        });
    }
    
    if (closeTargetPriceModalBtn) {
        closeTargetPriceModalBtn.addEventListener('click', function() {
            closeTargetPriceModal();
        });
    }
    
    if (saveTargetPriceBtn) {
        saveTargetPriceBtn.addEventListener('click', function() {
            saveTargetPrice();
        });
    }
    
    if (clearTargetPriceBtn) {
        clearTargetPriceBtn.addEventListener('click', function() {
            if (confirm('确定要清除此游戏的目标价格设置吗？')) {
                clearTargetPrice();
            }
        });
    }
    
    // 热门游戏相关事件绑定
    if (viewHotGamesBtn) {
        viewHotGamesBtn.addEventListener('click', function() {
            openHotGamesModal();
        });
    }
    
    if (closeHotGamesModalXBtn) {
        closeHotGamesModalXBtn.addEventListener('click', function() {
            closeHotGamesModal();
        });
    }
    
    if (closeHotGamesModalBtn) {
        closeHotGamesModalBtn.addEventListener('click', function() {
            closeHotGamesModal();
        });
    }
    
    // 点击模态框外部关闭
    document.addEventListener('click', function(event) {
        const modals = [
            { id: 'export-modal', closeFunc: closeExportModal },
            { id: 'add-game-modal', closeFunc: closeAddGameModal },
            { id: 'set-target-price-modal', closeFunc: closeTargetPriceModal },
            { id: 'hot-games-modal', closeFunc: closeHotGamesModal }
        ];
        
        // 检查点击的是否是模态框的背景
        modals.forEach(modal => {
            const modalElement = document.getElementById(modal.id);
            if (modalElement && event.target === modalElement) {
                modal.closeFunc();
            }
        });
    });

    // 不再在这里添加初始化日志，而是在loadMonitoredGames函数中添加
}

// 添加防抖函数，用于优化频繁触发的事件
function debounce(func, wait) {
    let timeout;
    return function(...args) {
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(this, args), wait);
    };
}

// 优化DOM元素缓存
const domElements = {};
function getElement(selector) {
    if (!domElements[selector]) {
        domElements[selector] = document.querySelector(selector);
    }
    return domElements[selector];
}

// 使用核心监控模块的monitorGameInfo函数
async function monitorGameInfo(game) {
    try {
        // 简单检查监控状态
        if (!isCdkMonitoring) return;
        
        // 调用核心模块的监控函数
        await window.cdkMonitorCore.monitorGameInfo(game);
        
    } catch (error) {
        addCdkLog(`监控游戏 ${game.name} 时发生错误: ${error.message}`, true);
        throw error;
    }
}

// 处理价格监控的核心逻辑
async function handlePriceMonitoring(game, gameIndex, currentUpdateTime) {
    // 简单检查监控状态
    if (!isCdkMonitoring) return;
    
    // 调用核心模块的价格监控函数
    await window.cdkMonitorCore.handlePriceMonitoring(game, gameIndex, currentUpdateTime);
}

// 获取游戏价格数据
async function fetchGamePrice(gameId, signal) {
    return await window.cdkMonitorCore.fetchGamePrice(gameId);
}

// 更新游戏价格数据
async function updateGamePriceData(game, gameIndex, lowestPrice, currentUpdateTime) {
    // 更新服务器数据
    await updateGameData(game.id, {
        current_price: lowestPrice,
        last_update_time: currentUpdateTime
    });
    
    // 在更新缓存前保存旧价格（避免引用修改问题）
    const oldPrice = monitoredGames[gameIndex].current_price;
    
    // 更新本地缓存
    monitoredGames[gameIndex].current_price = lowestPrice;
    monitoredGames[gameIndex].last_update_time = currentUpdateTime;
    
    // 记录价格变动（使用保存的旧价格值，并添加安全检查）
    if (oldPrice && oldPrice !== lowestPrice && typeof oldPrice === 'number' && typeof lowestPrice === 'number') {
        // 添加价格变动阈值检查，避免微小波动造成过多记录
        const priceChange = Math.abs(oldPrice - lowestPrice);
        const changePercentage = (priceChange / oldPrice) * 100;
        
        // 只记录变动超过0.01元或1%的价格变化
        if (priceChange >= 0.01 || changePercentage >= 1) {
            recordPriceChange(game.name, oldPrice, lowestPrice);
        }
    }
}

// 检查并处理目标价格命中 - 最高优先级处理
async function checkAndHandleTargetPrice(game, gameIndex, currentPrice, sellersData) {
    return await window.cdkMonitorCore.checkAndHandleTargetPrice(game, gameIndex, currentPrice, sellersData);
}

// 尝试自动下单
async function attemptAutoOrder(game, sellersData) {
    return await window.cdkMonitorCore.attemptAutoOrder(game, sellersData);
}

// 批量更新游戏数据，减少API调用
async function updateGameData(gameId, data) {
    return fetch(`/api/games/monitored/${gameId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });
}

// 优化渲染函数，使用DocumentFragment减少DOM操作
function renderMonitoredGames(games) {
    // 如果cdkMonitoredGamesContainer为null，尝试重新获取
    if (!cdkMonitoredGamesContainer) {
        cdkMonitoredGamesContainer = document.getElementById('cdk-monitored-games-container');
        if (!cdkMonitoredGamesContainer) {
            console.error('CDK Monitor: 无法找到游戏容器元素，渲染失败');
            return;
        }
    }
    
    // 使用DocumentFragment减少DOM重绘
    const fragment = document.createDocumentFragment();
    
    if (!games || games.length === 0) {
        const emptyMessage = document.createElement('div');
        emptyMessage.className = 'text-muted text-center py-4';
        emptyMessage.textContent = '监控列表为空，请添加游戏...';
        fragment.appendChild(emptyMessage);
    } else {
        let html = '';
        games.forEach(game => {
            const hasTarget = game.target_price && game.target_price > 0;
            const priceReached = hasTarget && game.current_price && game.current_price <= game.target_price;
            
            const cardClass = `game-monitor-card ${hasTarget ? 'has-target' : ''} ${priceReached ? 'price-reached' : ''}`;
            
            html += `
            <div class="${cardClass}" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || game.picUrl || ''}">
                <img src="${game.gameAva || '/static/images/default-game.png'}" class="game-avatar-lg" loading="lazy" />
                ${hasTarget ? `<span class="target-price-badge ${priceReached ? 'reached' : ''}">¥${game.target_price}</span>` : ''}
                <div class="game-info-block">
                  <div class="game-title">${game.name}</div>
                  ${game.game_group ? `<div class="game-group-badge"><small>🏷️ ${game.game_group}</small></div>` : ''}
                  <div class="game-actions-row">
                    <button class="remove-game" data-game-id="${game.id}" title="移除">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash-2"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                    </button>
                    <button class="set-target-price" data-game-id="${game.id}" data-game-name="${game.name}" data-game-ava="${game.gameAva || game.picUrl || ''}" title="设置目标价格">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-tag"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>
                    </button>
                  </div>
                  <div class="game-price-info">
                    ${game.current_price ? `<span class="current-price">当前: ¥${game.current_price}</span>` : '<span class="text-muted">等待价格数据...</span>'}
                    ${hasTarget ? `<span class="target-price">目标: ¥${game.target_price}</span>` : ''}
                  </div>
                </div>
            </div>`;
        });
        
        // 使用innerHTML一次性设置内容
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        
        // 将所有子节点移动到DocumentFragment
        while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
        }
    }
    
    // 一次性更新DOM
    cdkMonitoredGamesContainer.innerHTML = '';
    cdkMonitoredGamesContainer.appendChild(fragment);
    
    // 绑定事件
    bindGameCardEvents();
}

// 优化事件绑定，使用事件委托减少事件监听器数量
function bindGameCardEvents() {
    // 如果游戏容器为null，尝试重新获取
    if (!cdkMonitoredGamesContainer) {
        cdkMonitoredGamesContainer = document.getElementById('cdk-monitored-games-container');
        if (!cdkMonitoredGamesContainer) {
            console.error('CDK Monitor: 无法找到游戏容器元素，无法绑定事件');
            return;
        }
    }
    
    // 先移除旧的事件监听器，防止重复绑定
    cdkMonitoredGamesContainer.removeEventListener('click', handleGameCardClick);
    
    // 添加新的事件监听器
    cdkMonitoredGamesContainer.addEventListener('click', handleGameCardClick);
}

// 游戏卡片点击事件处理函数
function handleGameCardClick(e) {
    // 移除游戏按钮
    const removeBtn = e.target.closest('.remove-game');
    if (removeBtn) {
        e.stopPropagation(); // 阻止事件冒泡到卡片
        const gameId = removeBtn.dataset.gameId;
        if (confirm('确定要移除此游戏吗？')) {
            removeMonitoredGame(gameId);
        }
        return;
    }
    
    // 设置目标价格按钮
    const setPriceBtn = e.target.closest('.set-target-price');
    if (setPriceBtn) {
        e.stopPropagation(); // 阻止事件冒泡到卡片
        const gameId = setPriceBtn.dataset.gameId;
        const gameName = setPriceBtn.dataset.gameName;
        const gameAva = setPriceBtn.dataset.gameAva;
        openTargetPriceModal(gameId, gameName, gameAva);
        return;
    }
    
    // 卡片点击
    const gameCard = e.target.closest('.game-monitor-card');
    if (gameCard) {
        const gameId = gameCard.dataset.gameId;
        const gameName = gameCard.dataset.gameName;
        const gameAva = gameCard.dataset.gameAva;
        openTargetPriceModal(gameId, gameName, gameAva);
    }
}

// 添加监控日志
function addCdkLog(message, isError = false, isSuccess = false) {
    // 如果日志容器为null，尝试重新获取
    if (!cdkMonitorLogsContainer) {
        cdkMonitorLogsContainer = document.getElementById('cdk-monitor-logs');
        if (!cdkMonitorLogsContainer) {
            console.error('CDK Monitor: 无法找到日志容器元素，无法添加日志');
            return;
        }
    }
    
    // 如果有默认消息，先清除它
    const defaultMsg = cdkMonitorLogsContainer.querySelector('.text-muted');
    if (defaultMsg) {
        cdkMonitorLogsContainer.innerHTML = '';
    }

    const timestamp = new Date().toLocaleTimeString('zh-CN');
    
    // 添加到缓冲区
    logBuffer.push({
        message: message || '未知消息',
        timestamp,
        isError,
        isSuccess
    });
    
    // 如果缓冲区满了或者是重要消息，立即刷新
    if (logBuffer.length >= LOG_BUFFER_SIZE || isSuccess || isError) {
        flushLogBuffer();
    } else if (!logUpdateScheduled) {
        // 否则安排一个延迟刷新
        logUpdateScheduled = true;
        setTimeout(flushLogBuffer, 500);
    }
}

function flushLogBuffer() {
    // 如果日志容器为null，尝试重新获取
    if (!cdkMonitorLogsContainer) {
        cdkMonitorLogsContainer = document.getElementById('cdk-monitor-logs');
        if (!cdkMonitorLogsContainer || logBuffer.length === 0) return;
    } else if (logBuffer.length === 0) {
        return;
    }
    
    const fragment = document.createDocumentFragment();
    
    logBuffer.forEach(log => {
        const logEntry = document.createElement('div');
        logEntry.className = log.isError ? 'log-error' : (log.isSuccess ? 'log-success' : 'log-normal');
        
        const timestampSpan = document.createElement('span');
        timestampSpan.className = 'log-time';
        timestampSpan.textContent = `[${log.timestamp}]`;
        
        logEntry.appendChild(timestampSpan);
        logEntry.appendChild(document.createTextNode(' ' + log.message));
        
        fragment.appendChild(logEntry);
    });
    
    // 添加到日志容器顶部
    if (cdkMonitorLogsContainer.firstChild) {
        cdkMonitorLogsContainer.insertBefore(fragment, cdkMonitorLogsContainer.firstChild);
    } else {
        cdkMonitorLogsContainer.appendChild(fragment);
    }
    
    // 限制日志数量
    while (cdkMonitorLogsContainer.children.length > 100) {
        cdkMonitorLogsContainer.removeChild(cdkMonitorLogsContainer.lastChild);
    }
    
    // 清空缓冲区
    logBuffer.length = 0;
    logUpdateScheduled = false;
    
    // 自动滚动到顶部
    cdkMonitorLogsContainer.scrollTop = 0;
}

// 优化搜索游戏函数，添加防抖
const debouncedSearchGames = debounce(function(query) {
    // 确保cdkSearchResults存在
    if (!cdkSearchResults) {
        cdkSearchResults = document.getElementById('game-search-results');
        if (!cdkSearchResults) {
            console.error('CDK Monitor: 无法找到搜索结果容器元素');
            return;
        }
    }
    
    if (!query || query.length < 2) {
        cdkSearchResults.innerHTML = '<div class="text-center text-muted">请输入至少2个字符</div>';
        return;
    }
    
    // 显示加载中
    cdkSearchResults.innerHTML = '<div class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> 搜索中...</div>';
    
    fetch(`/api/games/search?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(games => {
            renderSearchResults(games);
        })
        .catch(error => {
            cdkSearchResults.innerHTML = `<div class="text-center text-danger">搜索失败: ${error.message}</div>`;
        });
}, 300);

// 添加内存管理优化
function cleanupMemory() {
    // 清理不再需要的大对象引用
    if (cdkMonitorLogsContainer) {
        const oldLogs = Array.from(cdkMonitorLogsContainer.children).slice(100);
        oldLogs.forEach(log => log.remove());
    }
    
    // 清理过期的localStorage数据
    const now = Date.now();
    Object.keys(localStorage).forEach(key => {
        if (key.startsWith('ordered_')) {
            try {
                const gameId = key.replace('ordered_', '');
                const game = monitoredGames.find(g => g.id === gameId);
                if (!game) {
                    // 如果游戏不在监控列表中，删除相关数据
                    localStorage.removeItem(key);
                }
            } catch (e) {
                console.error('清理localStorage出错:', e);
            }
        }
    });
}

// 定期清理内存
setInterval(cleanupMemory, 30 * 60 * 1000); // 每30分钟执行一次

// 添加清除已下单记录的按钮
const clearOrderedCdksBtn = document.getElementById('clear-ordered-cdks');
if (clearOrderedCdksBtn) {
    clearOrderedCdksBtn.addEventListener('click', function() {
        if (confirm('确定要清除所有已下单CDK记录吗？这可能导致重复下单！')) {
            // 清除所有以ordered_开头的localStorage项
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('ordered_')) {
                    localStorage.removeItem(key);
                }
            });
            addCdkLog('已清除所有已下单CDK记录', false, true);
        }
    });
}

// 添加热门游戏按钮和模态框相关元素
const viewHotGamesBtn = document.getElementById('view-hot-games');
const hotGamesModal = document.getElementById('hot-games-modal');
const closeHotGamesModalBtn = document.getElementById('close-hot-games-modal');
const closeHotGamesModalXBtn = document.getElementById('close-hot-games-modal-x');
const hotGamesContainer = document.getElementById('hot-games-container');

// 绑定热门游戏按钮事件
if (viewHotGamesBtn) {
    viewHotGamesBtn.addEventListener('click', function() {
        openHotGamesModal();
    });
}

if (closeHotGamesModalBtn) {
    closeHotGamesModalBtn.addEventListener('click', function() {
        closeHotGamesModal();
    });
}

if (closeHotGamesModalXBtn) {
    closeHotGamesModalXBtn.addEventListener('click', function() {
        closeHotGamesModal();
    });
}

// 通用模态框操作函数
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    // 使用Bootstrap模态框API或jQuery显示模态框（如果可用）
    if (typeof(bootstrap) !== 'undefined') {
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    } else if (typeof($) !== 'undefined') {
        $(modal).modal('show');
    } else {
        modal.style.display = 'block';
        modal.classList.add('show');
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;
    
    // 使用Bootstrap模态框API或jQuery隐藏模态框（如果可用）
    if (typeof(bootstrap) !== 'undefined') {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) modalInstance.hide();
    } else if (typeof($) !== 'undefined') {
        $(modal).modal('hide');
    } else {
        modal.style.display = 'none';
        modal.classList.remove('show');
    }
}

// 打开添加游戏模态框
function openAddGameModal() {
    const gameSearchInput = document.getElementById('game-search-input');
    const gameSearchResults = document.getElementById('game-search-results');
    
    showModal('add-game-modal');
    
    if (gameSearchInput) {
        gameSearchInput.value = '';
    }
    
    if (gameSearchResults) {
        gameSearchResults.innerHTML = '<div class="text-muted text-center py-4">请输入游戏名称进行搜索...</div>';
    }
    
    // 自动聚焦搜索框
    setTimeout(() => {
        if (gameSearchInput) {
            gameSearchInput.focus();
        }
    }, 100);
}

// 关闭添加游戏模态框
function closeAddGameModal() {
    hideModal('add-game-modal');
}

// 打开目标价格设置模态框
function openTargetPriceModal(gameId, gameName, gameAva) {
    currentGameId = gameId;
    const targetGameImg = document.getElementById('target-game-img');
    const targetGameName = document.getElementById('target-game-name');
    const targetPriceInput = document.getElementById('target-price-input');
    const priceNotificationType = document.getElementById('price-notification-type');
    const gameGroupInput = document.getElementById('game-group-input');
    
    // 设置游戏信息
    if (targetGameImg) {
        targetGameImg.src = gameAva || '/static/images/default-game.png';
    }
    if (targetGameName) {
        targetGameName.textContent = gameName || '未知游戏';
    }
    
    // 获取当前游戏的设置
    const game = monitoredGames.find(g => g.id === gameId);
    if (game) {
        // 设置目标价格输入框
        if (targetPriceInput && game.target_price) {
            targetPriceInput.value = game.target_price;
        } else if (targetPriceInput) {
            targetPriceInput.value = '';
        }
        
        // 设置通知类型
        if (priceNotificationType && game.price_notification_type) {
            priceNotificationType.value = game.price_notification_type;
        } else if (priceNotificationType) {
            priceNotificationType.value = 'highlight';
        }
        
        // 设置游戏分组
        if (gameGroupInput && game.game_group) {
            gameGroupInput.value = game.game_group;
        } else if (gameGroupInput) {
            gameGroupInput.value = '';
        }
    } else {
        // 清空表单
        if (targetPriceInput) {
            targetPriceInput.value = '';
        }
        if (priceNotificationType) {
            priceNotificationType.value = 'highlight';
        }
        if (gameGroupInput) {
            gameGroupInput.value = '';
        }
    }
    
    // 加载已有分组列表
    loadExistingGameGroups();
    
    showModal('set-target-price-modal');
    
    // 自动聚焦价格输入框
    setTimeout(() => {
        if (targetPriceInput) {
            targetPriceInput.focus();
        }
    }, 100);
}

// 关闭目标价格设置模态框
function closeTargetPriceModal() {
    hideModal('set-target-price-modal');
}

// 加载已有游戏分组列表
function loadExistingGameGroups() {
    fetch('/api/games/groups')
    .then(response => {
        if (!response.ok) {
            throw new Error('获取游戏分组失败');
        }
        return response.json();
    })
    .then(groups => {
        const dropdown = document.getElementById('existing-groups-dropdown');
        if (!dropdown) return;
        
        // 清空现有选项
        dropdown.innerHTML = '';
        
        if (groups.length === 0) {
            dropdown.innerHTML = '<div class="dropdown-item-text text-muted">暂无已有分组</div>';
        } else {
            groups.forEach(group => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.textContent = group;
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const gameGroupInput = document.getElementById('game-group-input');
                    if (gameGroupInput) {
                        gameGroupInput.value = group;
                    }
                });
                dropdown.appendChild(item);
            });
        }
    })
    .catch(error => {
        console.error('加载游戏分组失败:', error);
    });
}

// 打开导出模态框
function openExportModal() {
    const exportContentTextarea = document.getElementById('export-content');
    
    // 准备导出数据
    if (!monitoredGames || monitoredGames.length === 0) {
        alert('当前没有监控游戏可导出');
        return;
    }
    
    // 格式化导出的数据，只包含id, name 和 threshold (target_price)
    const exportData = monitoredGames.map(game => {
        return {
            name: game.name,
            id: game.id,
            threshold: game.target_price || 0
        };
    });
    
    // 格式化为带缩进的JSON字符串，使其易于阅读
    const exportText = JSON.stringify(exportData, null, 2);
    
    // 设置文本区域内容
    if (exportContentTextarea) {
        exportContentTextarea.value = exportText;
    }
    
    showModal('export-modal');
}

// 关闭导出模态框
function closeExportModal() {
    hideModal('export-modal');
}

// 打开热门游戏模态框
function openHotGamesModal() {
    showModal('hot-games-modal');
    
        // 重置为第一页
        hotGamesCurrentPage = 1;
        loadHotGames(hotGamesCurrentPage);
}

// 关闭热门游戏模态框
function closeHotGamesModal() {
    hideModal('hot-games-modal');
}

// 加载热门游戏列表
function loadHotGames(page = 1) {
    const hotGamesContainer = document.getElementById('hot-games-container');
    if (!hotGamesContainer) return;
    
    hotGamesCurrentPage = page;
    hotGamesContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border" role="status"></div><p class="mt-2">加载中...</p></div>';
    
    fetch(`/api/games/hot?pageNumber=${page}&pageSize=${hotGamesPageSize}&sort=cdkCount&order=asc`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`获取热门游戏列表失败: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 检查响应结构，适应不同的数据格式
            let gamesList = [];
            let totalPages = 1;
            
            if (data && data.success) {
                // 直接从API返回的结构
                if (data.content && Array.isArray(data.content)) {
                    gamesList = data.content;
                    totalPages = data.totalPages || Math.ceil(data.totalElements / hotGamesPageSize) || 1;
                }
                // 嵌套在result中的结构
                else if (data.result && data.result.content && Array.isArray(data.result.content)) {
                    gamesList = data.result.content;
                    totalPages = data.result.totalPages || Math.ceil(data.result.totalElements / hotGamesPageSize) || 1;
                }
            } else if (Array.isArray(data)) {
                // 直接返回数组的情况
                gamesList = data;
                totalPages = Math.ceil(data.length / hotGamesPageSize) || 1;
            }
            
            hotGamesTotalPages = totalPages;
            renderHotGames(gamesList);
            updateHotGamesPagination();
        })
        .catch(error => {
            console.error('加载热门游戏出错:', error);
            hotGamesContainer.innerHTML = `<div class="alert alert-danger text-center">获取热门游戏列表失败: ${error.message}</div>`;
        });
}

// 更新热门游戏分页控件
function updateHotGamesPagination() {
    const paginationElement = document.getElementById('hot-games-pagination');
    if (!paginationElement) return;
    
    const prevBtn = paginationElement.querySelector('.prev-page');
    const nextBtn = paginationElement.querySelector('.next-page');
    const pageIndicator = paginationElement.querySelector('.page-indicator');
    
    if (prevBtn) {
        prevBtn.disabled = hotGamesCurrentPage <= 1;
    }
    
    if (nextBtn) {
        nextBtn.disabled = hotGamesCurrentPage >= hotGamesTotalPages;
    }
    
    if (pageIndicator) {
        pageIndicator.textContent = `第 ${hotGamesCurrentPage} 页 / 共 ${hotGamesTotalPages} 页`;
    }
}

// 前往上一页
function goToHotGamesPrevPage() {
    if (hotGamesCurrentPage > 1) {
        loadHotGames(hotGamesCurrentPage - 1);
    }
}

// 前往下一页
function goToHotGamesNextPage() {
    if (hotGamesCurrentPage < hotGamesTotalPages) {
        loadHotGames(hotGamesCurrentPage + 1);
    }
}

// 渲染热门游戏列表
function renderHotGames(games) {
    const hotGamesContainer = document.getElementById('hot-games-container');
    if (!hotGamesContainer) return;
    
    if (!games || games.length === 0) {
        hotGamesContainer.innerHTML = '<div class="text-muted text-center py-4">暂无热门游戏数据</div>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>' +
               '<th>游戏名称</th><th>价格</th><th>更新时间</th><th>操作</th>' +
               '</tr></thead><tbody>';
               
    games.forEach(game => {
        // 根据实际返回的字段名称调整
        const gameName = game.gameName || game.name || '未知';
        const keyPrice = game.keyPrice || game.price || 0;
        const gameId = game.gameId || game.id || '';
        const picUrl = game.picUrl || game.gameAva || '';
        const updateTime = game.updateTime ? new Date(game.updateTime).toLocaleString() : '未知';
        
        // 检查游戏是否已在监控列表中
        const isAlreadyMonitored = monitoredGames.some(mg => mg.id === gameId);
        
        html += `
        <tr>
            <td class="d-flex align-items-center">
                <img src="${picUrl || '/static/images/default-game.png'}" alt="${gameName}" class="game-avatar-small mr-2" style="width: 30px; height: 30px;">
                <span>${gameName}</span>
            </td>
            <td>¥${keyPrice}</td>
            <td>${updateTime}</td>
            <td>
                <button class="btn btn-sm btn-${isAlreadyMonitored ? 'secondary' : 'primary'} add-to-monitor" 
                        data-game-id="${gameId}" 
                        data-game-name="${gameName}" 
                        data-game-ava="${picUrl}"
                        ${isAlreadyMonitored ? 'disabled' : ''}>
                    ${isAlreadyMonitored ? '已添加' : '添加监控'}
                </button>
            </td>
        </tr>`;
    });
    
    html += '</tbody></table></div>';
    
    // 添加分页控件
    html += `
    <div id="hot-games-pagination" class="d-flex justify-content-between align-items-center mt-3">
        <button class="btn btn-sm btn-outline-secondary prev-page" ${hotGamesCurrentPage <= 1 ? 'disabled' : ''}>
            <i class="icon-chevron-left"></i> 上一页
        </button>
        <span class="page-indicator">第 ${hotGamesCurrentPage} 页 / 共 ${hotGamesTotalPages} 页</span>
        <button class="btn btn-sm btn-outline-secondary next-page" ${hotGamesCurrentPage >= hotGamesTotalPages ? 'disabled' : ''}>
            下一页 <i class="icon-chevron-right"></i>
        </button>
    </div>`;
    
    hotGamesContainer.innerHTML = html;
    
    // 移除旧的事件监听器
    hotGamesContainer.removeEventListener('click', handleHotGamesClick);
    
    // 添加新的事件监听器，使用事件委托
    hotGamesContainer.addEventListener('click', handleHotGamesClick);
    
    // 绑定分页按钮事件
    const prevPageBtn = hotGamesContainer.querySelector('#hot-games-pagination .prev-page');
    const nextPageBtn = hotGamesContainer.querySelector('#hot-games-pagination .next-page');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', goToHotGamesPrevPage);
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', goToHotGamesNextPage);
    }
}

// 热门游戏点击事件处理函数
function handleHotGamesClick(e) {
    // 添加监控按钮
    const addBtn = e.target.closest('.add-to-monitor:not([disabled])');
    if (addBtn) {
        const gameId = addBtn.getAttribute('data-game-id');
        const gameName = addBtn.getAttribute('data-game-name');
        const gameAva = addBtn.getAttribute('data-game-ava') || '';
        
        if (gameId && gameName) {
            addGameToMonitor(gameId, gameName, gameAva);
            addBtn.textContent = '已添加';
            addBtn.classList.replace('btn-primary', 'btn-secondary');
            addBtn.disabled = true;
        } else {
            addCdkLog('添加监控失败：游戏数据不完整', true);
        }
    }
}

// 开始CDK监控
async function startCdkMonitoring() {
    if (isCdkMonitoring) return;
    
    if (!monitoredGames || monitoredGames.length === 0) {
        addCdkLog('没有可监控的游戏，请先添加游戏', true);
        return;
    }
    
    // 获取设置值
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const minInterval = parseInt(localStorage.getItem('cdkMinInterval') || (cdkMinIntervalInput ? cdkMinIntervalInput.value : '5000')) || 5000;
    const maxInterval = parseInt(localStorage.getItem('cdkMaxInterval') || (cdkMaxIntervalInput ? cdkMaxIntervalInput.value : '15000')) || 15000;
    
    isCdkMonitoring = true;
    const toggleCdkMonitorBtn = document.getElementById('toggle-cdk-monitor');
    if (toggleCdkMonitorBtn) {
        toggleCdkMonitorBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-stop-circle mr-1"><circle cx="12" cy="12" r="10"></circle><rect x="9" y="9" width="6" height="6"></rect></svg>停止监控`;
        toggleCdkMonitorBtn.classList.replace('btn-outline-success', 'btn-outline-danger');
    }
    
    addCdkLog(`开始监控 ${monitoredGames.length} 个游戏...`, false, true);

    // 统一批量监控模式：所有游戏使用统一间隔批量处理
    addCdkLog(`🚀 启用统一批量监控模式，${monitoredGames.length} 个游戏将每30秒批量监控一次`, false, true);
    await startUnifiedBatchMonitoring();
}

// 统一批量监控启动函数
async function startUnifiedBatchMonitoring() {
    if (!isCdkMonitoring || !monitoredGames.length) return;

    try {
        // 首次启动时立即进行一次批量监控
        addCdkLog(`📦 执行首次批量监控...`, false);
        await window.cdkMonitorCore.batchMonitorGameInfos(monitoredGames);

        // 设置统一的批量监控定时器
        const batchInterval = 30000; // 30秒间隔
        addCdkLog(`⏰ 设置统一监控间隔: ${batchInterval/1000}秒`, false, true);

        const batchMonitorTask = async () => {
            if (!isCdkMonitoring || !monitoredGames.length) return;

            try {
                addCdkLog(`🔄 开始新一轮批量监控 (${monitoredGames.length} 个游戏)`, false);
                await window.cdkMonitorCore.batchMonitorGameInfos(monitoredGames);
            } catch (error) {
                addCdkLog(`❌ 批量监控执行失败: ${error.message}`, true);
            }
        };

        // 设置统一定时器
        cdkBatchMonitorTimer = setInterval(batchMonitorTask, batchInterval);

    } catch (error) {
        addCdkLog(`❌ 统一批量监控启动失败: ${error.message}`, true);
        // 回退到传统单个监控模式
        addCdkLog(`🔄 回退到单个游戏监控模式`, false);
        monitoredGames.forEach(game => {
            startMonitoringGame(game);
        });
    }
}

// 开始监控单个游戏
function startMonitoringGame(game) {
    const minInterval = 5000;  // 5秒
    const maxInterval = 15000; // 15秒
    
    // 开始监控
    const monitorTask = async () => {
        // 立即检查监控状态，确保能够快速响应停止命令
        if (!isCdkMonitoring) return;
        
        let retryCount = 0;
        const MAX_RETRIES = 3;
        
        while (retryCount <= MAX_RETRIES && isCdkMonitoring) { // 添加监控状态检查
            try {
                // 再次检查监控状态
                if (!isCdkMonitoring) return;
                
                await monitorGameInfo(game);
                break; // 成功则退出重试循环
            } catch (error) {
                // 如果已停止监控，立即退出
                if (!isCdkMonitoring) return;
                
                retryCount++;
                const waitTime = retryCount * 2000; // 指数退避
                
                if (retryCount <= MAX_RETRIES) {
                    addCdkLog(`监控游戏 ${game.name} 失败，${retryCount}/${MAX_RETRIES} 次重试，等待 ${waitTime/1000} 秒...`, true);
                    
                    // 使用可中断的等待方式
                    const waitPromise = new Promise(resolve => {
                        const waitTimer = setTimeout(resolve, waitTime);
                        // 将等待计时器也添加到监控计时器对象中以便可以清除
                        cdkMonitorTimers[`${game.id}_wait_${retryCount}`] = waitTimer;
                    });
                    
                    await waitPromise;
                    
                    // 等待结束后再次检查监控状态
                    if (!isCdkMonitoring) return;
                } else {
                    addCdkLog(`监控游戏 ${game.name} 失败 ${MAX_RETRIES} 次，暂停此游戏监控`, true);
                }
            }
        }
        
        // 如果仍在监控中，继续下一次监控
        if (isCdkMonitoring) {
            // 随机生成下一次监控间隔
            const nextInterval = Math.floor(Math.random() * (maxInterval - minInterval + 1)) + minInterval;
            cdkMonitorTimers[game.id] = setTimeout(monitorTask, nextInterval);
        }
    };
    
    // 立即执行一次，然后设置定时器
    monitorTask();
}

// 停止CDK监控
function stopCdkMonitoring() {
    // 立即设置监控状态为false
    isCdkMonitoring = false;
    
    const toggleCdkMonitorBtn = document.getElementById('toggle-cdk-monitor');
    if (toggleCdkMonitorBtn) {
        toggleCdkMonitorBtn.innerHTML = '<i class="icon-play mr-1"></i>开始监控';
        toggleCdkMonitorBtn.classList.remove('btn-outline-danger');
        toggleCdkMonitorBtn.classList.add('btn-outline-success');
        toggleCdkMonitorBtn.disabled = true; // 临时禁用按钮防止重复点击
    }
    
    // 清除统一批量监控定时器
    if (cdkBatchMonitorTimer) {
        clearInterval(cdkBatchMonitorTimer);
        cdkBatchMonitorTimer = null;
        addCdkLog('🛑 统一批量监控定时器已清除', false);
    }

    // 清除所有单个游戏定时器（兼容性保留）
    Object.keys(cdkMonitorTimers).forEach(gameId => {
        clearTimeout(cdkMonitorTimers[gameId]);
        delete cdkMonitorTimers[gameId];
    });

    addCdkLog('CDK监控已立即停止', false, true);
    
    // 1秒后重新启用按钮
    setTimeout(() => {
        if (toggleCdkMonitorBtn) {
            toggleCdkMonitorBtn.disabled = false;
        }
    }, 1000);
}

// 搜索游戏
function searchGames() {
    const gameSearchInput = document.getElementById('game-search-input');
    
    // 确保cdkSearchResults存在
    if (!cdkSearchResults) {
        cdkSearchResults = document.getElementById('game-search-results');
        if (!cdkSearchResults) {
            console.error('CDK Monitor: 无法找到搜索结果容器元素');
            return;
        }
    }
    
    if (!gameSearchInput) return;
    
    const searchTerm = gameSearchInput.value.trim();
    if (!searchTerm) {
        cdkSearchResults.innerHTML = '<div class="text-warning text-center py-4">请输入游戏名称</div>';
        return;
    }
    
    cdkSearchResults.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="sr-only">搜索中...</span></div></div>';
    
    fetch(`/api/games/search?query=${encodeURIComponent(searchTerm)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('搜索游戏失败');
            }
            return response.json();
        })
        .then(results => {
            if (!results || results.length === 0) {
                cdkSearchResults.innerHTML = '<div class="text-muted text-center py-4">未找到相关游戏</div>';
                return;
            }
            
            let html = '<div class="list-group">';
            results.forEach(game => {
                const isAlreadyMonitored = monitoredGames.some(mg => mg.id === game.id);
                html += `
                <div class="list-group-item d-flex align-items-center">
                    <img src="${game.picUrl || '/static/images/default-game.png'}" alt="${game.gameName}" class="game-avatar-small mr-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-0">${game.gameName}</h6>
                        <small class="text-muted">ID: ${game.id}</small>
                    </div>
                    <button class="btn btn-sm btn-${isAlreadyMonitored ? 'secondary' : 'primary'} add-to-monitor" 
                            data-game-id="${game.id}" 
                            data-game-name="${game.gameName}" 
                            data-game-ava="${game.gameAva || game.picUrl || ''}"
                            ${isAlreadyMonitored ? 'disabled' : ''}>
                        ${isAlreadyMonitored ? '已添加' : '添加监控'}
                    </button>
                </div>`;
            });
            html += '</div>';
            
            cdkSearchResults.innerHTML = html;
            
            // 移除旧的事件监听器
            cdkSearchResults.removeEventListener('click', handleSearchResultsClick);
            
            // 添加新的事件监听器，使用事件委托
            cdkSearchResults.addEventListener('click', handleSearchResultsClick);
        })
        .catch(error => {
            cdkSearchResults.innerHTML = `<div class="text-danger text-center py-4">搜索失败: ${error.message}</div>`;
        });
}

// 搜索结果点击事件处理函数
function handleSearchResultsClick(e) {
    // 添加监控按钮
    const addBtn = e.target.closest('.add-to-monitor:not([disabled])');
    if (addBtn) {
        const gameId = addBtn.getAttribute('data-game-id');
        const gameName = addBtn.getAttribute('data-game-name');
        const gameAva = addBtn.getAttribute('data-game-ava');
        
        if (gameId && gameName) {
            addGameToMonitor(gameId, gameName, gameAva);
            addBtn.textContent = '已添加';
            addBtn.classList.replace('btn-primary', 'btn-secondary');
            addBtn.disabled = true;
        } else {
            addCdkLog('添加监控失败：游戏数据不完整', true);
        }
    }
}

// 从监控列表移除游戏
function removeMonitoredGame(gameId, skipConfirm = false) {
    // 确认框已经在事件处理函数中处理，这里不再需要
    
    fetch(`/api/games/monitored/${gameId}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('从监控列表中移除游戏失败');
        }
        
        // 如果正在监控，先停止该游戏的监控
        if (cdkMonitorTimers[gameId]) {
            clearTimeout(cdkMonitorTimers[gameId]);
            delete cdkMonitorTimers[gameId];
        }
        
        // 从本地缓存中移除
        monitoredGames = monitoredGames.filter(game => game.id !== gameId);
        
        // 重新渲染整个列表，而不是单独删除元素
        renderMonitoredGames(monitoredGames);
        
        addCdkLog(`游戏ID ${gameId} 已从监控列表中移除`, false);
    })
    .catch(error => {
        addCdkLog('从监控列表中移除游戏失败: ' + error.message, true);
    });
}

// 保存目标价格设置
function saveTargetPrice() {
    if (!currentGameId) {
        addCdkLog('无法保存：游戏ID为空', true);
        return;
    }
    
    const targetPriceInput = document.getElementById('target-price-input');
    const priceNotificationType = document.getElementById('price-notification-type');
    const gameGroupInput = document.getElementById('game-group-input');
    
    if (!targetPriceInput) {
        addCdkLog('无法保存：目标价格输入框不存在', true);
        return;
    }
    
    const targetPrice = parseFloat(targetPriceInput.value);
    if (isNaN(targetPrice) || targetPrice <= 0) {
        alert('请输入有效的目标价格');
        targetPriceInput.focus();
        return;
    }
    
    const notificationType = priceNotificationType ? priceNotificationType.value : 'highlight';
    const gameGroup = gameGroupInput ? gameGroupInput.value.trim() : '';
    
    // 构建更新数据
    const updateData = { 
        target_price: targetPrice,
        price_notification_type: notificationType
    };
    
    // 只有当游戏分组有变化时才添加到更新数据中
    const currentGame = monitoredGames.find(game => game.id === currentGameId);
    const currentGameGroup = currentGame ? currentGame.game_group : null;
    
    if (gameGroup !== currentGameGroup) {
        updateData.game_group = gameGroup || null;
    }
    
    // 保存到服务器
    fetch(`/api/games/monitored/${currentGameId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('更新目标价格失败');
        }
        return response.json();
    })
    .then(result => {
        // 更新本地缓存
        const gameIndex = monitoredGames.findIndex(game => game.id === currentGameId);
        if (gameIndex !== -1) {
            monitoredGames[gameIndex].target_price = targetPrice;
            monitoredGames[gameIndex].price_notification_type = notificationType;
            if (gameGroup) {
                monitoredGames[gameIndex].game_group = gameGroup;
            } else {
                delete monitoredGames[gameIndex].game_group;
            }
        }
        
        // 重新渲染游戏列表
        renderMonitoredGames(monitoredGames);
        
        // 关闭模态框
        closeTargetPriceModal();
        
        // 添加日志
        let logMessage = `游戏ID ${currentGameId} 的目标价格已设置为 ${targetPrice}`;
        if (gameGroup) {
            logMessage += `，分组: ${gameGroup}`;
        }
        addCdkLog(logMessage, false, true);
        
        // 调试：验证分组信息是否正确保存
        console.log(`[DEBUG] 游戏 ${currentGameId} 分组信息:`, {
            输入的分组: gameGroup,
            当前分组: currentGameGroup,
            是否更新: gameGroup !== currentGameGroup,
            更新数据: updateData
        });
    })
    .catch(error => {
        addCdkLog('设置目标价格失败: ' + error.message, true);
    });
}

// 清除目标价格设置
function clearTargetPrice() {
    if (!currentGameId) {
        addCdkLog('无法清除：游戏ID为空', true);
        return;
    }
    
    // 保存到服务器
    fetch(`/api/games/monitored/${currentGameId}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
            target_price: null,
            price_notification_type: null
            // 注意：不包含 game_group，避免意外清除分组信息
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('清除目标价格失败');
        }
        return response.json();
    })
    .then(result => {
        // 更新本地缓存
        const gameIndex = monitoredGames.findIndex(game => game.id === currentGameId);
        if (gameIndex !== -1) {
            delete monitoredGames[gameIndex].target_price;
            delete monitoredGames[gameIndex].price_notification_type;
        }
        
        // 重新渲染游戏列表
        renderMonitoredGames(monitoredGames);
        
        // 关闭模态框
        closeTargetPriceModal();
        
        // 添加日志
        addCdkLog(`游戏ID ${currentGameId} 的目标价格已清除`, false);
    })
    .catch(error => {
        addCdkLog('清除目标价格失败: ' + error.message, true);
    });
}

// 复制导出内容
function copyExportContent() {
    const exportContentTextarea = document.getElementById('export-content');
    const copyExportBtn = document.getElementById('copy-export-btn');
    
    if (!exportContentTextarea || !copyExportBtn) return;
    
    exportContentTextarea.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const originalText = copyExportBtn.textContent;
    copyExportBtn.textContent = '已复制!';
    copyExportBtn.classList.add('btn-success');
    copyExportBtn.classList.remove('btn-primary');
    
    // 2秒后恢复按钮状态
    setTimeout(() => {
        copyExportBtn.textContent = originalText;
        copyExportBtn.classList.remove('btn-success');
        copyExportBtn.classList.add('btn-primary');
    }, 2000);
}

// 加载CDK监控设置
function loadCdkSettings() {
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const cdkAutoOrderCheckbox = document.getElementById('cdk-auto-order');
    const cdkStopAfterOrderCheckbox = document.getElementById('cdk-stop-after-order');
    
    // 从localStorage加载设置
    const minInterval = localStorage.getItem('cdkMinInterval') || 5000;
    const maxInterval = localStorage.getItem('cdkMaxInterval') || 15000;
    // 将自动下单默认为打开状态
    const autoOrder = localStorage.getItem('cdkAutoOrder') !== 'false'; // 默认为 true
    // 下单后停止监控默认为关闭状态（保持原有行为）
    const stopAfterOrder = localStorage.getItem('cdkStopAfterOrder') === 'true'; // 默认为 false
    
    // 设置表单值
    if (cdkMinIntervalInput) cdkMinIntervalInput.value = minInterval;
    if (cdkMaxIntervalInput) cdkMaxIntervalInput.value = maxInterval;
    if (cdkAutoOrderCheckbox) cdkAutoOrderCheckbox.checked = autoOrder;
    if (cdkStopAfterOrderCheckbox) cdkStopAfterOrderCheckbox.checked = stopAfterOrder;
}

// 保存CDK监控设置
function saveCdkSettings() {
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const cdkAutoOrderCheckbox = document.getElementById('cdk-auto-order');
    const cdkStopAfterOrderCheckbox = document.getElementById('cdk-stop-after-order');
    
    // 获取表单值
    const minInterval = cdkMinIntervalInput ? cdkMinIntervalInput.value : 5000;
    const maxInterval = cdkMaxIntervalInput ? cdkMaxIntervalInput.value : 15000;
    const autoOrder = cdkAutoOrderCheckbox ? cdkAutoOrderCheckbox.checked : true;
    const stopAfterOrder = cdkStopAfterOrderCheckbox ? cdkStopAfterOrderCheckbox.checked : false;
    
    // 验证设置
    if (parseInt(minInterval) > parseInt(maxInterval)) {
        addCdkLog('设置错误: 最小间隔不能大于最大间隔', true);
        return;
    }
    
    // 保存到localStorage
    localStorage.setItem('cdkMinInterval', minInterval);
    localStorage.setItem('cdkMaxInterval', maxInterval);
    localStorage.setItem('cdkAutoOrder', autoOrder);
    localStorage.setItem('cdkStopAfterOrder', stopAfterOrder);
    
    // 显示保存成功消息
    addCdkLog('监控设置已保存', false, true);
    if (autoOrder) {
        addCdkLog('自动下单已启用，当游戏价格达到目标时将自动下单', false, true);
    } else {
        addCdkLog('自动下单已禁用', false);
    }
    
    if (stopAfterOrder) {
        addCdkLog('下单成功后将自动停止监控该游戏', false, true);
    } else {
        addCdkLog('下单成功后将继续监控该游戏', false);
    }
}

// 加载价格变动记录
function loadCdkPriceChanges() {
    // 如果价格变动容器为null，尝试重新获取
    if (!cdkPriceChangesContainer) {
        cdkPriceChangesContainer = document.getElementById('cdk-price-changes');
        if (!cdkPriceChangesContainer) {
            console.error('CDK Monitor: 无法找到价格变动容器元素，无法加载价格变动');
            return;
        }
    }
    
    const storedChanges = localStorage.getItem('cdkPriceChanges');
    if (storedChanges) {
        try {
            const priceChanges = JSON.parse(storedChanges);
            // 只显示最近20条记录
            const displayChanges = priceChanges.slice(0, 20);
            renderPriceChanges(displayChanges);
        } catch (e) {
            console.error('解析价格变动记录失败', e);
            cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">解析价格变动记录失败</td></tr>';
        }
    } else {
        cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无价格变动记录...</td></tr>';
    }
}

// 渲染价格变动记录
function renderPriceChanges(changes) {
    // 如果价格变动容器为null，尝试重新获取
    if (!cdkPriceChangesContainer) {
        cdkPriceChangesContainer = document.getElementById('cdk-price-changes');
        if (!cdkPriceChangesContainer) {
            console.error('CDK Monitor: 无法找到价格变动容器元素，无法渲染价格变动');
            return;
        }
    }
    
    if (!changes || changes.length === 0) {
        cdkPriceChangesContainer.innerHTML = '<tr class="text-center text-muted"><td colspan="5">暂无价格变动记录...</td></tr>';
        return;
    }
    
    let html = '';
    changes.forEach((change, index) => {
        const priceChange = change.newPrice - change.oldPrice;
        const priceChangeClass = priceChange > 0 ? 'text-danger' : (priceChange < 0 ? 'text-success' : '');
        
        html += `
        <tr>
            <td>${change.gameName}</td>
            <td>${change.oldPrice.toFixed(2)}</td>
            <td class="${priceChangeClass}">${change.newPrice.toFixed(2)}</td>
            <td>${change.timestamp}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-details-btn" data-game-id="${change.gameId}" title="查看详情">
                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                </button>
            </td>
        </tr>`;
    });
    
    cdkPriceChangesContainer.innerHTML = html;
    
    // 绑定查看详情按钮事件
    cdkPriceChangesContainer.querySelectorAll('.view-details-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const gameId = this.getAttribute('data-game-id');
            if (gameId) {
                window.open(`/game/${gameId}`, '_blank');
            }
        });
    });
}

// 加载监控游戏列表
function loadMonitoredGames() {
    // 如果游戏容器为null，尝试重新获取
    if (!cdkMonitoredGamesContainer) {
        cdkMonitoredGamesContainer = document.getElementById('cdk-monitored-games-container');
    }
    
    // 显示加载中的提示
    if (cdkMonitoredGamesContainer) {
        cdkMonitoredGamesContainer.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">加载中...</p></div>';
    } else {
        console.error('CDK Monitor: 无法找到游戏容器元素，无法加载游戏列表');
        return;
    }
    
    fetch('/api/games/monitored')
        .then(response => {
            if (!response.ok) {
                throw new Error('获取监控游戏列表失败');
            }
            return response.json();
        })
        .then(games => {
            monitoredGames = games;
            // 只调用一次renderMonitoredGames
            renderMonitoredGames(games);
            
            // 添加适当的日志提示
            if (games && games.length > 0) {
                addCdkLog(`已加载 ${games.length} 个监控游戏`, false, true);
            } else {
                addCdkLog('监控列表为空，请添加游戏...', false);
            }
        })
        .catch(error => {
            console.error('获取监控游戏列表失败:', error);
            addCdkLog('获取监控游戏列表失败: ' + (error.message || '未知错误'), true);
            
            // 显示错误消息在容器中
            if (cdkMonitoredGamesContainer) {
                cdkMonitoredGamesContainer.innerHTML = '<div class="text-danger text-center py-4">获取游戏列表失败，请刷新页面重试</div>';
            }
        });
}

// 添加游戏到监控列表
function addGameToMonitor(gameId, gameName, gameAva) {
    const cdkMinIntervalInput = document.getElementById('cdk-min-interval');
    const cdkMaxIntervalInput = document.getElementById('cdk-max-interval');
    const minInterval = parseInt(cdkMinIntervalInput?.value || '5000') || 5000;
    const maxInterval = parseInt(cdkMaxIntervalInput?.value || '15000') || 15000;
    
    const newGame = {
        id: gameId,
        name: gameName,
        gameAva: gameAva,
        min_interval: minInterval,
        max_interval: maxInterval
    };
    
    fetch('/api/games/monitored', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(newGame)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('添加游戏到监控列表失败');
        }
        return response.json();
    })
    .then(game => {
        addCdkLog(`游戏 ${gameName} 已添加到监控列表`, false, true);
        loadMonitoredGames();
        
        // 更新按钮状态
        const btn = document.querySelector(`.add-to-monitor[data-game-id="${gameId}"]`);
        if (btn) {
            btn.textContent = '已添加';
            btn.classList.replace('btn-primary', 'btn-secondary');
            btn.disabled = true;
        }
    })
    .catch(error => {
        addCdkLog('添加游戏到监控列表失败: ' + (error.message || '未知错误'), true);
    });
}

// 下单购买CDK
async function placeCdkOrder(gameName, saleId, actualPrice = 0) {
    return await window.cdkMonitorCore.placeCdkOrder(gameName, saleId, actualPrice);
}

// 添加价格变动记录函数
function addPriceChange(game, newPrice) {
    // 直接获取价格变化
    let oldPrice = 0;
    const gameIndex = monitoredGames.findIndex(g => g.id === game.id);
    if (gameIndex !== -1 && monitoredGames[gameIndex].current_price) {
        oldPrice = monitoredGames[gameIndex].current_price;
    }
    
    // 创建新的价格变动记录对象
    const timestamp = new Date().toLocaleString('zh-CN');
    const newChange = {
        gameId: game.id,
        gameName: game.name,
        oldPrice: oldPrice,
        newPrice: newPrice,
        timestamp: timestamp
    };
    
    // 添加到价格变化表格的顶部
    addPriceChangeToUI(newChange);
}

// 添加单条价格变化记录到UI顶部
function addPriceChangeToUI(change) {
    // 如果价格变动容器为null，尝试重新获取
    if (!cdkPriceChangesContainer) {
        cdkPriceChangesContainer = document.getElementById('cdk-price-changes');
        if (!cdkPriceChangesContainer) {
            console.error('CDK Monitor: 无法找到价格变动容器元素，无法添加价格变动记录');
            return;
        }
    }

    // 如果表格为空，清除"暂无记录"消息
    const emptyMsg = cdkPriceChangesContainer.querySelector('.text-muted');
    if (emptyMsg && emptyMsg.parentElement.tagName === 'TR' && emptyMsg.parentElement.cells.length === 1) {
        cdkPriceChangesContainer.innerHTML = '';
    }
    
    // 创建新的表格行
    const row = document.createElement('tr');
    
    // 计算价格变化
    const priceChange = change.newPrice - change.oldPrice;
    const priceChangeClass = priceChange > 0 ? 'text-danger' : (priceChange < 0 ? 'text-success' : '');
    
    // 设置行内容
    row.innerHTML = `
        <td>${change.gameName}</td>
        <td>${change.oldPrice.toFixed(2)}</td>
        <td class="${priceChangeClass}">${change.newPrice.toFixed(2)}</td>
        <td>${change.timestamp}</td>
        <td>
            <button class="btn btn-sm btn-outline-primary view-details-btn" data-game-id="${change.gameId}" title="查看详情">
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg>
            </button>
        </td>
    `;
    
    // 添加到表格顶部
    if (cdkPriceChangesContainer.firstChild) {
        cdkPriceChangesContainer.insertBefore(row, cdkPriceChangesContainer.firstChild);
    } else {
        cdkPriceChangesContainer.appendChild(row);
    }
    
    // 绑定查看详情按钮事件
    const viewBtn = row.querySelector('.view-details-btn');
    if (viewBtn) {
        viewBtn.addEventListener('click', function() {
            const gameId = this.getAttribute('data-game-id');
            window.open(`/game/${gameId}`, '_blank');
        });
    }
    
    // 限制显示的行数，最多显示100条
    while (cdkPriceChangesContainer.children.length > 100) {
        cdkPriceChangesContainer.removeChild(cdkPriceChangesContainer.lastChild);
    }
}

// 发送价格通知
function sendPriceNotification(game, currentPrice) {
    if (!game) return;
    
    try {
        // 根据通知类型选择通知方式
        const notificationType = game.price_notification_type || 'highlight';
        
        switch (notificationType) {
            case 'browser':
                // 浏览器通知
                showPriceNotification(game, currentPrice);
                break;
            case 'highlight':
                // 高亮显示（默认行为，通过CSS实现）
                break;
            default:
                // 默认行为，仅日志记录
                break;
        }
    } catch (error) {
        console.error('发送价格通知失败:', error);
    }
}

// 显示浏览器通知
function showPriceNotification(game, currentPrice) {
    // 检查浏览器是否支持通知
    if (!("Notification" in window)) {
        console.warn("此浏览器不支持桌面通知");
        return;
    }
    
    // 检查通知权限
    if (Notification.permission === "granted") {
        // 已授权，直接显示通知
        createNotification(game, currentPrice);
    } else if (Notification.permission !== "denied") {
        // 请求权限
        Notification.requestPermission().then(permission => {
            if (permission === "granted") {
                createNotification(game, currentPrice);
            }
        });
    }
    
    // 创建通知
    function createNotification(game, price) {
        const notification = new Notification(`${game.name} 价格达到目标`, {
            body: `当前价格: ¥${price}, 目标价格: ¥${game.target_price}`,
            icon: game.gameAva || '/static/images/default-game.png'
        });
        
        // 点击通知打开游戏页面
        notification.onclick = function() {
            window.open(`/game/${game.id}`, '_blank');
            notification.close();
        };
        
        // 10秒后自动关闭
        setTimeout(() => {
            notification.close();
        }, 10000);
    }
}

// 将 loadMonitoredGames 函数暴露到全局作用域
window.loadMonitoredGames = loadMonitoredGames;
