from fastapi import APIRouter, Depends, Query, HTTPException, Body
from typing import List, Dict, Any, Optional
import logging
from pydantic import BaseModel

from pywatcher.core.steampy_client import SteamPyClient
from pywatcher.api.dependencies import get_steam_py_client, get_cdk_query_client
from pywatcher.crud import config_manager
from pywatcher.models.config import GameFavorite, GameMonitor
from pywatcher.models.game import Game, GameFavoriteCreate, GameFavoriteReorder

router = APIRouter(
    prefix="/games",
    tags=["Games"]
)

@router.get("/search", response_model=List[Game], response_model_by_alias=False)
async def search_games(
    term: str = Query(..., min_length=1, description="要搜索的游戏名称", alias="query"),
    client: SteamPyClient = Depends(get_cdk_query_client)
):
    """
    根据关键词在 steampy.com 搜索游戏。
    """
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未提供有效的认证凭据 (accesstoken)。")

    try:
        logging.info(f"正在搜索游戏: {term}")
        results = await client.search_game(game_name=term)
        
        if results and isinstance(results, dict) and results.get("code") == 200:
            result_data = results.get("result", {})
            return result_data.get("content", []) if isinstance(result_data, dict) else []
        
        # 处理API业务错误或意外格式
        error_msg = results.get("message", "游戏搜索失败") if isinstance(results, dict) else "上游API响应格式无效"
        status_code = results.get("code", 502) if isinstance(results, dict) else 502
        logging.warning(f"搜索游戏API响应异常: {results}")
        raise HTTPException(status_code=status_code, detail=error_msg)

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"搜索游戏时发生意外错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="搜索游戏时发生内部服务器错误。")


# --- Favorite Games Endpoints ---

@router.get("/favorites", response_model=List[GameFavorite])
async def list_favorites():
    """获取所有收藏的游戏"""
    return config_manager.get_favorite_games()


@router.post("/favorites", response_model=GameFavorite, status_code=201)
async def add_favorite(
    game_create: GameFavoriteCreate = Body(...)
):
    """将一个游戏添加到收藏列表"""
    # 将传入的 GameFavoriteCreate 转换为内部存储用的 GameFavorite
    game_to_save = GameFavorite.model_validate(game_create.model_dump())
    
    await config_manager.add_favorite_game(game_to_save)
    return game_to_save


@router.delete("/favorites/{game_id}", status_code=204)
async def remove_from_favorites(game_id: str):
    """从收藏列表中移除一个游戏"""
    success = await config_manager.remove_favorite_game(game_id)
    if not success:
        raise HTTPException(status_code=404, detail="收藏的游戏未找到")
    return None # 204 No Content 响应体应为空


@router.post("/favorites/reorder", status_code=200, response_model=Dict[str, str])
async def reorder_favorites(payload: GameFavoriteReorder = Body(...)):
    """更新收藏列表的游戏顺序"""
    await config_manager.update_favorite_games_order(payload.ordered_game_ids)
    return {"message": "收藏列表顺序已更新"}


# --- CDK监控游戏 Endpoints ---

class GameMonitorCreate(BaseModel):
    id: str
    name: str
    gameAva: str
    min_interval: int = 5000
    max_interval: int = 15000
    auto_order: bool = False
    game_group: Optional[str] = None

class GameMonitorUpdate(BaseModel):
    min_interval: int = None
    max_interval: int = None
    last_update_time: str = None
    auto_order: bool = None
    target_price: Optional[float] = None
    price_notification_type: Optional[str] = None
    current_price: Optional[float] = None
    game_group: Optional[str] = None

@router.get("/monitored", response_model=List[GameMonitor])
async def list_monitored_games():
    """获取所有CDK监控游戏"""
    return config_manager.get_monitored_games()

@router.post("/monitored", response_model=GameMonitor, status_code=201)
async def add_monitored_game(
    game_create: GameMonitorCreate = Body(...)
):
    """将一个游戏添加到CDK监控列表"""
    # 将传入的数据转换为内部存储用的GameMonitor
    game_to_save = GameMonitor.model_validate(game_create.model_dump())
    
    await config_manager.add_monitored_game(game_to_save)
    return game_to_save

@router.delete("/monitored/{game_id}", status_code=204)
async def remove_from_monitored(game_id: str):
    """从CDK监控列表中移除一个游戏"""
    success = await config_manager.remove_monitored_game(game_id)
    if not success:
        raise HTTPException(status_code=404, detail="监控的游戏未找到")
    return None

@router.patch("/monitored/{game_id}", status_code=200, response_model=Dict[str, Any])
async def update_monitored_game(
    game_id: str,
    updates: GameMonitorUpdate = Body(...)
):
    """更新CDK监控游戏的设置"""
    # 过滤出非None值
    update_data = {k: v for k, v in updates.model_dump().items() if v is not None}
    
    if not update_data:
        return {"message": "无更新内容"}
    
    success = await config_manager.update_monitored_game(game_id, update_data)
    if not success:
        raise HTTPException(status_code=404, detail="监控的游戏未找到")
    
    return {"message": "游戏监控设置已更新", "updates": update_data}

@router.get("/{game_id}/info", response_model=Dict[str, Any])
async def get_game_info(
    game_id: str,
    client: SteamPyClient = Depends(get_cdk_query_client)  # 使用CDK专用查询账号
):
    """获取指定游戏的详细信息，使用CDK专用查询账号池 - 性能优化版本"""
    if not client.access_token:
        raise HTTPException(status_code=401, detail="未登录，请先登录后再使用此功能。")

    try:
        response = await client.get_game_info(game_id=game_id)

        if response and isinstance(response, dict):
            # 性能优化：快速提取updateTime，避免多次字段查找
            update_time = response.get("updateTime")
            if not update_time and "result" in response:
                result = response["result"]
                if isinstance(result, dict):
                    update_time = result.get("updateTime")

            # 性能优化：直接返回最小必要数据
            return {
                "updateTime": update_time,
                "result": response.get("result", {})
            }
        return {"updateTime": None}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取游戏信息失败: {str(e)}")

# --- Seller Info Endpoint ---

@router.get("/{game_id}/sellers", response_model=List[Dict[str, Any]])
async def get_game_sellers(
    game_id: str,
    pageSize: int = Query(5, description="每页显示条数"),
    client: SteamPyClient = Depends(get_cdk_query_client)
):
    """获取指定游戏的卖家列表"""
    if not client.access_token:
        logging.warning(f"尝试获取游戏 {game_id} 的卖家列表，但未提供有效的认证凭据。")
        raise HTTPException(status_code=401, detail="未登录，请先登录后再使用此功能。")

    logging.info(f"正在获取游戏 {game_id} 的卖家列表，数量限制: {pageSize}")
    try:
        response = await client.get_game_sellers(game_id=game_id, page_size=pageSize)
        
        if response and response.get("code") == 200:
            content = response.get("result", {}).get("content", [])
            logging.info(f"成功获取游戏 {game_id} 的 {len(content)} 个卖家信息")
            return content
        elif response and response.get("code") == 401:
            logging.warning(f"获取卖家列表失败: 认证失败 (401). 错误信息: {response.get('message')}")
            raise HTTPException(status_code=401, detail=response.get("message", "认证失败或令牌无效。"))
        elif response and response.get("code") != 200:
            logging.warning(f"获取卖家列表失败，错误码 {response.get('code')}. 错误信息: {response.get('message')}")
            raise HTTPException(
                status_code=response.get("code", 400), 
                detail=response.get("message", f"获取游戏 {game_id} 的卖家列表失败。")
            )
        elif response and "error" in response:
            logging.error(f"获取卖家列表失败，客户端错误: {response.get('error')}. 详情: {response.get('details')}")
            raise HTTPException(status_code=502, detail={"message": "API请求失败", "error": response["error"], "details": response.get("details")})
        else:
            logging.error(f"获取游戏 {game_id} 卖家列表时，API客户端返回了意外的空或无效响应。")
            raise HTTPException(status_code=500, detail=f"获取游戏 {game_id} 卖家列表时发生意外错误。")
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"获取卖家列表时发生意外错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取卖家列表时发生内部错误: {str(e)}")


@router.get("/{game_id}/price", response_model=Dict[str, Any])
async def get_game_price(
    game_id: str,
    game_name: str = Query(None, description="游戏名称，用于日志记录"),
    client: SteamPyClient = Depends(get_cdk_query_client)
):
    """获取指定游戏的最低价格"""
    if not client.access_token:
        logging.warning(f"尝试获取游戏 {game_id} 的价格，但未提供有效的认证凭据。")
        raise HTTPException(status_code=401, detail="未登录，请先登录后再使用此功能。")

    logging.info(f"正在获取游戏 {game_id} 的价格")
    try:
        price = await client.fetch_game_price(game_id=game_id, game_name=game_name)
        return {"game_id": game_id, "price": price, "success": True}
    except Exception as e:
        logging.error(f"获取游戏价格时发生意外错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取游戏价格时发生内部错误: {str(e)}")


# 添加缓存价格的模型
class PriceCacheRequest(BaseModel):
    game_id: str
    name: str
    price: float

@router.post("/cache-price", response_model=Dict[str, Any])
async def cache_price(data: PriceCacheRequest):
    """将游戏价格保存到缓存数据库"""
    logging.info(f"缓存游戏 {data.name} (ID: {data.game_id}) 的价格: {data.price}")
    
    try:
        # 直接使用sqlite3保存到pywatcher.db
        import sqlite3
        import os
        import time
        
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 从当前文件路径获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        
        # 数据库文件路径
        db_path = os.path.join(project_root, "data", "game_prices.db")
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS game_prices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            gameId TEXT UNIQUE,
            gameName TEXT NOT NULL,
            keyPrice REAL NOT NULL,
            lastUpdate TEXT NOT NULL
        )
        ''')
        
        # 获取当前时间
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # 插入新记录或更新已有记录
        cursor.execute('''
        INSERT OR REPLACE INTO game_prices (gameId, gameName, keyPrice, lastUpdate)
        VALUES (?, ?, ?, ?)
        ''', (data.game_id, data.name, data.price, current_time))
        
        # 提交并关闭连接
        conn.commit()
        
        # 获取数据库中的记录数
        cursor.execute('SELECT COUNT(*) FROM game_prices')
        count = cursor.fetchone()[0]
        
        conn.close()
        
        return {
            "success": True, 
            "message": f"价格已更新到缓存，当前共有 {count} 条记录"
        }
    except Exception as e:
        logging.error(f"更新价格缓存失败: {e}", exc_info=True)
        return {"success": False, "message": f"更新价格缓存失败: {str(e)}"}

# 添加获取所有缓存价格的API端点
@router.get("/cached-prices", response_model=List[Dict[str, Any]])
async def get_cached_prices(
    gameIds: List[str] = Query(None, description="要获取价格的游戏ID列表")
):
    """获取缓存的游戏价格数据，如果提供了gameIds参数，则只返回指定ID的价格"""
    try:
        import sqlite3
        import os
        
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 从当前文件路径获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_file)))
        
        # 数据库文件路径
        db_path = os.path.join(project_root, "data", "game_prices.db")
        
        # 如果数据库文件不存在，返回空列表
        if not os.path.exists(db_path):
            logging.warning(f"缓存数据库不存在: {db_path}")
            return []
        
        # 连接数据库
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row  # 启用行工厂，结果可以通过列名访问
        cursor = conn.cursor()
        
        if gameIds:
            # 只查询指定游戏ID的价格
            placeholders = ','.join('?' * len(gameIds))
            query = f'SELECT gameId as game_id, gameName as name, keyPrice as price, lastUpdate as updated_at FROM game_prices WHERE gameId IN ({placeholders})'
            cursor.execute(query, gameIds)
            logging.info(f"查询指定的 {len(gameIds)} 个游戏ID的缓存价格")
        else:
            # 查询所有缓存的价格数据
            cursor.execute('SELECT gameId as game_id, gameName as name, keyPrice as price, lastUpdate as updated_at FROM game_prices')
            logging.info("查询所有游戏的缓存价格")
        
        rows = cursor.fetchall()
        
        # 转换为字典列表
        result = [dict(row) for row in rows]
        
        # 关闭连接
        conn.close()
        
        return result
    except Exception as e:
        logging.error(f"获取缓存价格数据失败: {e}", exc_info=True)
        return [] 


# --- 游戏分组管理接口 ---

class GameGroupRequest(BaseModel):
    game_group: str

@router.get("/groups", response_model=List[str])
async def get_all_game_groups():
    """获取所有游戏分组名称"""
    try:
        groups = config_manager.get_all_game_groups()
        return groups
    except Exception as e:
        logging.error(f"获取游戏分组失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取游戏分组失败: {str(e)}")

@router.get("/groups/{group_name}", response_model=List[GameMonitor])
async def get_games_by_group(group_name: str):
    """获取指定分组的所有游戏"""
    try:
        games = config_manager.get_games_by_group(group_name)
        return games
    except Exception as e:
        logging.error(f"获取分组游戏失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取分组游戏失败: {str(e)}")

@router.delete("/groups/{group_name}", response_model=Dict[str, Any])
async def remove_games_by_group(group_name: str):
    """移除指定分组的所有游戏"""
    try:
        removed_game_ids = await config_manager.remove_games_by_group(group_name)
        return {
            "message": f"已成功移除分组 '{group_name}' 中的 {len(removed_game_ids)} 个游戏",
            "removed_game_ids": removed_game_ids,
            "group_name": group_name
        }
    except Exception as e:
        logging.error(f"移除分组游戏失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"移除分组游戏失败: {str(e)}")


