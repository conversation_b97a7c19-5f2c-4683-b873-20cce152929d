import aiohttp
import json
import asyncio
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

class SteamPyClient:
    """
    与 steampy.com 后端 API 交互的异步客户端。
    该实现基于 Web/steampy_client.py 以确保兼容性。
    """
    BASE_URL = "https://steampy.com/xboot"

    def __init__(self, session: aiohttp.ClientSession, access_token: Optional[str] = None):
        self.session = session
        self.access_token = access_token
        if self.access_token:
            logger.info(f"SteamPyClient is using an access token.")

    async def _send_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """统一的请求发送方法 - 性能优化版本"""
        url = f"{self.BASE_URL}{endpoint}"

        headers = kwargs.get("headers", {})
        if self.access_token:
            headers["accesstoken"] = self.access_token
        kwargs["headers"] = headers

        try:
            async with self.session.request(method, url, **kwargs) as response:
                # 性能优化：直接解析JSON，减少Content-Type检查
                if response.status < 400:
                    return await response.json()
                else:
                    # 错误处理
                    try:
                        error_data = await response.json()
                        return {"error": f"HTTP {response.status}", "status_code": response.status, "details": error_data}
                    except:
                        text_content = await response.text()
                        return {"error": f"HTTP {response.status}", "status_code": response.status, "details": text_content}

        except aiohttp.ClientResponseError as e:
            return {"error": str(e), "status_code": e.status, "message": e.message}
        except Exception as e:
            logger.error(f"Request error for {url}: {e}")
            return {"error": f"Unexpected error: {e}"}

    async def search_game(self, game_name: str, page_number: int = 1, page_size: int = 100) -> Dict[str, Any]:
        """根据名称在 steampy.com 搜索游戏。"""
        logger.info(f"Searching for game: {game_name}...")
        endpoint = "/steamGame/keyByName"
        params = {
            "pageNumber": page_number, 
            "pageSize": page_size,
            "sort": "keyTx", 
            "order": "asc", 
            "gameName": game_name,
        }
        return await self._send_request("GET", endpoint, params=params)

    async def get_game_info(self, game_id: str) -> Dict[str, Any]:
        """
        获取指定游戏的详细信息 - 性能优化版本
        API: /xboot/steamGame/getOne
        """
        endpoint = "/steamGame/getOne"
        params = {"gameId": game_id}
        return await self._send_request("GET", endpoint, params=params)

    async def get_game_sellers(self, game_id: str, page_size: int = 5) -> Dict[str, Any]:
        """
        获取指定游戏的卖家列表.
        API Doc: /xboot/steamKeySale/listSale
        """
        if not self.access_token:
            logger.error("Cannot get game sellers without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}

        logger.info(f"Fetching sellers for game ID: {game_id}")
        endpoint = "/steamKeySale/listSale"
        params = {
            "pageNumber": 1,
            "pageSize": page_size,
            "sort": "keyPrice",
            "order": "asc",
            "gameId": game_id,
        }
        
        # 使用通用发送请求方法
        return await self._send_request("GET", endpoint, params=params)

    async def get_chests(self, page_number: int = 1, page_size: int = 10, 
                         sort: str = "sortOrder", order: str = "asc") -> Dict[str, Any]:
        """
        获取肥皂盒记录列表
        API: /xboot/chest/showPageV2
        """
        if not self.access_token:
            logger.error("Cannot get chests without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching chests (page {page_number}, sort by {sort}, order {order})")
        endpoint = "/chest/showPageV2"
        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
            "sort": sort,
            "order": order,
        }
        
        return await self._send_request("GET", endpoint, params=params)

    # --- 保留其他方法的桩代码，以防其他部分调用 ---

    async def get_login_sms(self, phone: str) -> Dict[str, Any]:
        logger.info(f"Requesting SMS code for phone: {phone}...")
        endpoint = f"/common/captcha/sendLoginSms/{phone}"
        return await self._send_request("GET", endpoint)

    async def verify_login_code(self, phone: str, code: str):
        """使用短信验证码登录，返回值与Web版本相同 (token, response)"""
        logger.info(f"Attempting login with phone: {phone}...")
        endpoint = "/user/smsLogin"
        data = {"mobile": phone, "code": code}
        response_data = await self._send_request("POST", endpoint, data=data)
        
        if response_data and "error" in response_data:
            logger.error(f"Login request failed: {response_data['error']}. Details: {response_data.get('details')}")
            return None, response_data

        if response_data and response_data.get("code") == 200:
            logger.info("Login successful.")
            token = response_data.get("result")
            self.access_token = token
            # 更新请求头中的token
            if self.access_token:
                logger.info(f"SteamPyClient is using a new access token.")
            return token, response_data
        else:
            msg = response_data.get('message', 'Unknown API error during login')
            logger.error(f"Login API error: Code {response_data.get('code')}, Message: {msg}")
            return None, response_data

    async def fetch_game_price(self, game_id: str, game_name: str = None) -> float:
        """
        获取游戏价格，通过调用get_game_sellers API
        """
        logger.info(f"Fetching price for game: {game_name or game_id}")
        try:
            sellers_response = await self.get_game_sellers(game_id=game_id)
            
            if not isinstance(sellers_response, dict):
                logger.error(f"Unexpected response type from get_game_sellers: {type(sellers_response)}")
                return 0.0
            
            if sellers_response.get("code") != 200:
                error_message = sellers_response.get("message", "Unknown error")
                logger.error(f"Failed to fetch game price: {error_message}")
                return 0.0
            
            # 从结果中提取卖家数据并找到最低价格
            content = sellers_response.get("result", {}).get("content", [])
            if not content:
                logger.warning(f"No sellers found for game {game_name or game_id}")
                return 0.0
            
            # 找出所有有效价格中的最低价
            valid_prices = [
                seller.get("keyPrice") for seller in content
                if seller.get("keyPrice") is not None and isinstance(seller.get("keyPrice"), (int, float))
            ]
            
            if not valid_prices:
                logger.warning(f"No valid prices found for game {game_name or game_id}")
                return 0.0
            
            min_price = min(valid_prices)
            logger.info(f"Found minimum price {min_price} for game {game_name or game_id}")
            return min_price
        except Exception as e:
            logger.error(f"Error in fetch_game_price for {game_name or game_id}: {e}", exc_info=True)
            return 0.0

    async def get_market_item_price(self, market_hash_name: str) -> Optional[float]:
        logger.warning("get_market_item_price is a placeholder and not fully implemented.")
        return 0.0
        
    async def get_chest_details(self, chest_id: str) -> Dict[str, Any]:
        """
        获取肥皂盒详情
        API: /xboot/chest/showOne
        """
        if not self.access_token:
            logger.error(f"Cannot get chest details for {chest_id} without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching details for chest: {chest_id}")
        endpoint = "/chest/showOne"
        params = {"chestId": chest_id}
        
        return await self._send_request("GET", endpoint, params=params)

    async def get_chest_logs(self, chest_id: str = None, page_number: int = 1, page_size: int = 40,
                           sort: str = "createTime", order: str = "desc") -> Dict[str, Any]:
        """
        获取肥皂盒记录
        API: /xboot/detLog/show
        
        参数：
        - chest_id: 肥皂盒ID，为空则获取所有肥皂盒的记录
        - page_number: 页码
        - page_size: 每页条数
        - sort: 排序字段
        - order: 排序方向（asc/desc）
        """
        if not self.access_token:
            logger.error("Cannot get chest logs without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching logs for chest: {chest_id or 'all'}")
        endpoint = "/detLog/show"
        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
            "sort": sort,
            "order": order
        }
        
        # 只有当chest_id不为空时才添加到参数中
        if chest_id:
            params["chestId"] = chest_id
        
        return await self._send_request("GET", endpoint, params=params)
        
    async def get_chest_games(self, chest_id: str, page_number: int = 1, page_size: int = 100,
                           sort_order: str = "lv", order_direction: str = "asc") -> Dict[str, Any]:
        """
        获取肥皂盒游戏列表
        API: /xboot/chest/showGame
        """
        if not self.access_token:
            logger.error(f"Cannot get chest games for {chest_id} without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录"}
            
        logger.info(f"Fetching games for chest: {chest_id}")
        endpoint = "/chest/showGame"
        params = {
            "chestId": chest_id,
            "pageNumber": page_number,
            "pageSize": page_size,
            "sort": sort_order,
            "order": order_direction
        }
        
        return await self._send_request("GET", endpoint, params=params)
        
    async def place_order(self, sale_id: str, pay_type: str = "AA", wallet_flag: str = "") -> Dict[str, Any]:
        """
        下单购买游戏
        API: /xboot/steamKeyOrder/payOrder
        
        参数:
            sale_id: 订单ID
            pay_type: 支付方式，AA=支付宝，WX=微信
            wallet_flag: 是否使用余额，useBalance=使用，空字符串=不使用
            
        返回:
            API 响应结果
        """
        if not self.access_token:
            logger.error("Cannot place order without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录", "success": False}
            
        logger.info(f"Placing order for sale ID: {sale_id}, payment: {pay_type}, use balance: {'Yes' if wallet_flag else 'No'}")
        endpoint = "/steamKeyOrder/payOrder"
        
        # 构建请求参数
        params = {
            "saleId": sale_id,
            "payType": pay_type,
            "promoCodeId": "",
            "walletFlag": wallet_flag,
            "version": "v1"
        }
        
        # 发送请求
        response = await self._send_request("POST", endpoint, params=params)
        
        # 处理响应
        if isinstance(response, dict):
            if response.get("code") == 200:
                response["success"] = True
            else:
                response["success"] = False
                
        return response

    async def check_payment_status(self, order_id: str) -> Dict[str, Any]:
        """
        检查订单支付状态
        API: /xboot/steamKeyOrder/checkPay
        
        参数:
            order_id: 订单ID
            
        返回:
            API 响应结果
        """
        if not self.access_token:
            logger.error("Cannot check payment status without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录", "success": False}
            
        logger.info(f"Checking payment status for order ID: {order_id}")
        endpoint = f"/steamKeyOrder/checkPay?orderId={order_id}"
        
        # 发送请求
        response = await self._send_request("POST", endpoint)
        
        # 处理响应
        if isinstance(response, dict):
            if response.get("code") == 200:
                response["success"] = True
            else:
                response["success"] = False
                
        return response

    async def redeem_key(self, order_id: str, player: str = "HEIXS21", password: str = "", save_log: bool = False) -> Dict[str, Any]:
        """
        兑换订单激活码
        API: /xboot/steamKeyOrder/keyFirstV2
        
        参数:
            order_id: 订单ID
            player: 玩家名称
            password: 密码(可选)
            save_log: 是否保存日志
            
        返回:
            API 响应结果
        """
        if not self.access_token:
            logger.error("Cannot redeem key without an access token.")
            return {"error": "Not logged in", "code": 401, "message": "未登录", "success": False}
            
        logger.info(f"Redeeming key for order ID: {order_id}, player: {player}")
        endpoint = "/steamKeyOrder/keyFirstV2"
        
        # 构建请求参数
        params = {
            "orderId": order_id,
            "player": player,
            "password": password,
            "saveLog": str(save_log).lower()
        }
        
        # 发送请求
        response = await self._send_request("POST", endpoint, data=params)
        
        # 处理响应
        if isinstance(response, dict):
            if response.get("code") == 200:
                response["success"] = True
            else:
                response["success"] = False
                
        return response

    async def get_captcha_id(self):
        """获取验证码ID"""
        url = f"{self.BASE_URL}/common/captcha/init"
        headers = {"User-Agent": "APPAPK"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                result = await response.json()
                return result.get("result")

    async def get_captcha_image(self, captcha_id: str):
        """获取图形验证码图片"""
        url = f"{self.BASE_URL}/common/captcha/draw/{captcha_id}"
        headers = {"User-Agent": "Dalvik/2.1.0 (Linux; U; Android 12; PFGM00 Build/f89a2a9.0)"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                return await response.read()

    async def send_register_sms(self, phone: str, captcha_id: str, captcha_code: str):
        """发送注册短信验证码"""
        url = f"{self.BASE_URL}/common/captcha/sendRegistAppSms/{phone}"
        params = {"captchaId": captcha_id, "code": captcha_code}
        headers = {"User-Agent": "APPAPK"}
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params, headers=headers) as response:
                return await response.json()

    async def register_with_sms(self, phone: str, sms_code: str):
        """使用短信验证码注册/登录"""
        url = f"{self.BASE_URL}/user/smsAppLogin"
        params = {
            "cid": "",
            "mobile": phone,
            "code": sms_code,
            "inviter": "",
            "eventId": "",
            "saveLogin": "true",
            "mobileLog": "true"
        }
        headers = {"User-Agent": "APPAPK"}
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, params=params, headers=headers) as response:
                return await response.json()


